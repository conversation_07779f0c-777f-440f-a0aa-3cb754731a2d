version: "2"
run:
  allow-parallel-runners: true
linters:
  enable:
    # 基础错误检查
    - errcheck # checking for unchecked errors, these unchecked errors can be critical bugs in some cases
    - govet # reports suspicious constructs, such as Printf calls whose arguments do not align with the format string
    - staticcheck # is a go vet on steroids, applying a ton of static analysis checks
    - unused # checks for unused constants, variables, functions and types
    - ineffassign # detects when assignments to existing variables are not used
    - staticcheck # is a go vet on steroids, applying a ton of static analysis checks
    - goconst # finds repeated strings that could be replaced by a constant
    - revive # fast, configurable, extensible, flexible, and beautiful linter for Go, drop-in replacement of golint
    - lll # reports long lines
    - funlen # tool for detection of long functions
    # 代码质量
    - gocyclo # detects cyclomatic complexity
    - gocognit # detects cognitive complexity
    - dupl # detects duplicate code
    - misspell # checks spelling mistakes
    # 代码风格
    - whitespace # checks for unnecessary whitespace
    - nlreturn # checks for unnecessary return statements
  disable:
    - asasalint
    - asciicheck
    - bidichk
    - bodyclose
    - cyclop
    - dupl
    - durationcheck
    - errname
    - errorlint
    - exhaustive
    - forbidigo
    - gocheckcompilerdirectives
    - gochecknoglobals
    - gochecknoinits
    - gochecksumtype
    - gocognit
    - gocritic
    - gocyclo
    - godot
    - gomoddirectives
    - gomodguard
    - goprintffuncname
    - gosec
    - loggercheck
    - makezero
    - mirror
    - mnd
    - musttag
    - nakedret
    - nestif
    - nilerr
    - nilnil
    - noctx
    - nolintlint
    - nonamedreturns
    - nosprintfhostport
    - perfsprint
    - predeclared
    - promlinter
    - protogetter
    - reassign
    - rowserrcheck
    - sloglint
    - spancheck
    - sqlclosecheck
    - testableexamples
    - testifylint
    - testpackage
    - tparallel
    - unconvert
    - unparam
    - usestdlibvars
    - wastedassign
    - whitespace
  settings:
    errcheck:
      check-type-assertions: true
    funlen:
      lines: 120
      statements: 120
    goconst:
      min-len: 2
      min-occurrences: 2
    gocritic:
      enabled-checks:
        - nestingReduce
      settings:
        nestingReduce:
          bodyWidth: 5
    gocyclo:
      min-complexity: 10
    lll:
      line-length: 120
      tab-width: 4
    staticcheck:
      checks:
        - all
        - -SA1019
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    paths:
      - backend/test/api_integration
      - backend/common/rpc
      - cli/devops_executor/cmd
      - template/template-go
      - tmp
      - backend/app/voice_agent
      - third_party$
      - builtin$
      - examples$
formatters:
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
