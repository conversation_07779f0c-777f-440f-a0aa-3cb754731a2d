package service

import (
	"context"

	helloworldpb "github.com/MoeGolibrary/moego/backend/proto/helloworld/v1"
	"github.com/MoeGolibrary/moego/template/template-go/logic/greeter"
)

type Greeter struct {
	gr *greeter.Logic
	helloworldpb.UnimplementedHelloworldServiceServer
}

func NewGreeter() *Greeter {
	return &Greeter{
		gr: greeter.NewLogic(),
	}
}

func (g Greeter) SendPing(ctx context.Context,
	_ *helloworldpb.SendPingRequest) (*helloworldpb.SendPingResponse, error) {
	msg, err := g.gr.Hello(ctx)
	if err != nil {
		return nil, err
	}
	return &helloworldpb.SendPingResponse{Pong: msg}, nil
}
