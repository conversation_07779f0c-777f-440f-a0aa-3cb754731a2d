package stripe

import "github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"

type Client interface{}

// 这里主要是为了演示 config 的使用，以 获取 stripe 的配置信息为例
type impl struct {
	api<PERSON>ey, host string
}

func New() Client {
	c, err := config.Load(
		// 这里是配置文件的路径
		"./stripe.yaml",
		// 这里是配置文件的编码格式
		config.WithCodec("yaml"),
		// 这里是抽象的配置中心，一般是 file，也可以是 etcd, redis, growthbook 等
		config.WithProvider("file"))
	if err != nil {
		panic(err)
	}
	apiKey, ok := c.Get("api_key", "default_api_key").(string)
	if !ok {
		panic("api_key is not a string")
	}
	host, ok := c.Get("host", "default_host").(string)
	if !ok {
		panic("host is not a string")
	}
	return &impl{
		apiKey: a<PERSON><PERSON><PERSON>,
		host:   host,
	}
}
