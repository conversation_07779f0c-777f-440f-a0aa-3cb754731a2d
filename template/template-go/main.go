package main

import (
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/config/nacos"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/validation"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	helloworldpb "github.com/MoeGolibrary/moego/backend/proto/helloworld/v1"
	"github.com/MoeGolibrary/moego/template/template-go/service"
)

func main() {
	s := rpc.NewServer()

	// 这里需要注册grpc服务
	grpc.Register(s, &helloworldpb.HelloworldService_ServiceDesc, service.NewGreeter())

	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
