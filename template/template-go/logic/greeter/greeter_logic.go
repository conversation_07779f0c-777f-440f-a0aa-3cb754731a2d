package greeter

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/template/template-go/repo/gorm"
)

type Logic struct {
	gorm gorm.ReadWriter
}

func NewLogic() *Logic {
	return &Logic{gorm: gorm.New()}
}

// NewByParams is used to create a new Logic instance with parameters
// This method is for unit test to mock dependencies
func NewByParams(gorm gorm.ReadWriter) *Logic {
	return &Logic{gorm: gorm}
}

// Hello is a test method
func (l *Logic) Hello(ctx context.Context) (string, error) {
	// this is a test
	msg := "Hello"
	// log message
	// use framework logger
	log.DebugContextf(ctx, msg)
	// return Hello
	return msg, nil
}
