package fulfillment

import (
	"context"

	"github.com/MoeGolibrary/go-lib/zlog"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/converter/impl"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/util"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gormx"

	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/query"
)

var StaffTimeSlotConverter = &impl.StaffTimeSlotConverter{}

//go:generate mockgen -package=fulfillment -destination=mocks/mock_staff_time_slot_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment StaffTimeSlotRepository
type StaffTimeSlotRepository interface {
	WithQuery(q *query.Query) StaffTimeSlotRepository

	BatchCreate(ctx context.Context, models []*model.StaffTimeSlot) error
	BatchUpdate(ctx context.Context, models []*model.StaffTimeSlot) error
	BatchUpdateSelective(ctx context.Context, updates []dto.UpdateStaffTimeSlotDTO) error
	ListByFilter(ctx context.Context, filter filter.ListStaffTimeSlotFilter) ([]*model.StaffTimeSlot, error)
	BatchSoftDelete(ctx context.Context, ids []int64) error
	DeleteByFulfillmentID(ctx context.Context, fulfillmentID int64) error
}

const BatchSize = 100

type staffTimeSlotRepository struct {
	query *query.Query
}

func (r staffTimeSlotRepository) BatchUpdateSelective(ctx context.Context, updates []dto.UpdateStaffTimeSlotDTO) error {
	if len(updates) == 0 {
		return nil
	}
	for _, update := range updates {
		_, err := r.query.WithContext(ctx).StaffTimeSlot.
			Where(r.query.StaffTimeSlot.ID.Eq(update.StaffTimeSlotID)).
			Updates(gormx.Update(StaffTimeSlotConverter.UpdateToOpt(update)))
		if err != nil {
			return err
		}
	}
	return nil
}

func (r staffTimeSlotRepository) ListByFilter(ctx context.Context, filter filter.ListStaffTimeSlotFilter) ([]*model.StaffTimeSlot, error) {
	if filter.CompanyID == nil || len(filter.BusinessIDs) == 0 || filter.StartTimeMin == nil || filter.StartTimeMax == nil {
		if len(filter.CareTypes) == 0 || len(filter.DetailIDs) == 0 {
			if len(filter.FulfillmentIDs) == 0 {
				if len(filter.StaffTimeSlotIDs) == 0 {
					zlog.Info(ctx, "Invalid filter",
						zap.Reflect("filter", filter),
					)
					return []*model.StaffTimeSlot{}, nil
				}
			}
		}
	}
	selectQuery := r.query.WithContext(ctx).StaffTimeSlot.Select()
	if len(filter.StaffTimeSlotIDs) != 0 {
		selectQuery.Where(r.query.StaffTimeSlot.ID.In(filter.StaffTimeSlotIDs...))
	}
	if filter.CompanyID != nil {
		selectQuery.Where(r.query.StaffTimeSlot.CompanyID.Eq(*filter.CompanyID))
	}
	if len(filter.BusinessIDs) != 0 {
		selectQuery.Where(r.query.StaffTimeSlot.BusinessID.In(filter.BusinessIDs...))
	}
	if filter.StartTimeMin != nil {
		selectQuery.Where(r.query.StaffTimeSlot.StartDatetime.Gte(*filter.StartTimeMin))
	}
	if filter.StartTimeMax != nil {
		selectQuery.Where(r.query.StaffTimeSlot.StartDatetime.Lte(*filter.StartTimeMax))
	}
	if len(filter.CareTypes) != 0 {
		selectQuery.Where(r.query.StaffTimeSlot.CareType.In(util.EnumSliceValuer(filter.CareTypes)...))
	}
	if len(filter.PetIDs) != 0 {
		selectQuery.Where(r.query.StaffTimeSlot.PetID.In(filter.PetIDs...))
	}
	if len(filter.DetailIDs) != 0 {
		selectQuery.Where(r.query.StaffTimeSlot.DetailID.In(filter.DetailIDs...))
	}
	if len(filter.FulfillmentIDs) != 0 {
		selectQuery.Where(r.query.StaffTimeSlot.FulfillmentID.In(filter.FulfillmentIDs...))
	}
	result, err := selectQuery.Find()
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (r staffTimeSlotRepository) WithQuery(q *query.Query) StaffTimeSlotRepository {
	if q != nil {
		return &staffTimeSlotRepository{query: q}
	}
	return &staffTimeSlotRepository{query: r.query}
}

func (r staffTimeSlotRepository) BatchCreate(ctx context.Context, models []*model.StaffTimeSlot) error {
	return r.query.WithContext(ctx).StaffTimeSlot.CreateInBatches(models, BatchSize)
}

func (r staffTimeSlotRepository) BatchUpdate(ctx context.Context, models []*model.StaffTimeSlot) error {
	for _, staffTimeSlot := range models {
		_, err := r.query.WithContext(ctx).StaffTimeSlot.
			Where(r.query.StaffTimeSlot.ID.Eq(staffTimeSlot.ID)).
			Updates(staffTimeSlot)
		if err != nil {
			return err
		}
	}
	return nil
}

func (r staffTimeSlotRepository) BatchSoftDelete(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}
	_, err := r.query.WithContext(ctx).StaffTimeSlot.
		Where(r.query.StaffTimeSlot.ID.In(ids...)).
		Delete()
	return err
}

func (r staffTimeSlotRepository) DeleteByFulfillmentID(ctx context.Context, fulfillmentID int64) error {
	_, err := r.query.WithContext(ctx).StaffTimeSlot.
		Where(r.query.StaffTimeSlot.FulfillmentID.Eq(fulfillmentID)).
		Delete()
	return err
}

func NewStaffTimeSlotRepository(db *gorm.DB) StaffTimeSlotRepository {
	return &staffTimeSlotRepository{
		query: query.Use(db),
	}
}
