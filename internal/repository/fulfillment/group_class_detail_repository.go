package fulfillment

import (
	"context"

	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/converter"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/converter/impl"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/util"
	"gorm.io/gorm"
	"gorm.io/gormx"

	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/query"
)

var GroupClassDetailConverter = &impl.GroupClassConverter{}

//go:generate mockgen -package=fulfillment -destination=mocks/mock_group_class_detail_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment GroupClassDetailRepository
type GroupClassDetailRepository interface {
	WithQuery(q *query.Query) GroupClassDetailRepository

	Create(ctx context.Context, model *model.GroupClassDetail) error
	Update(ctx context.Context, model *model.GroupClassDetail) error
	BatchUpdateSelective(ctx context.Context, updates []dto.UpdateGroupClassDetailDTO) error
	GetByID(ctx context.Context, id int64) (*model.GroupClassDetail, error)
	ListByFilter(ctx context.Context, filter filter.ListGroupClassDetailFilter) ([]*model.GroupClassDetail, error)
	CountByInstanceID(ctx context.Context, groupClassInstanceID int64) (int64, error)
	GetByPetAndInstance(ctx context.Context, petID int64, instanceID int64) (*model.GroupClassDetail, error)
	DeleteByPetAndInstance(ctx context.Context, petID int64, instanceID int64) error
}

type groupClassDetailRepository struct {
	query *query.Query
}

func (r groupClassDetailRepository) CountByInstanceID(ctx context.Context, groupClassInstanceID int64) (int64, error) {
	count, err := r.query.WithContext(ctx).GroupClassDetail.
		Join(r.query.Fulfillment, r.query.GroupClassDetail.FulfillmentID.EqCol(r.query.Fulfillment.ID)).
		Where(r.query.Fulfillment.Status.In(util.EnumSliceValuer(converter.GetPrePaymentActiveStatuses)...)).
		Where(r.query.GroupClassDetail.GroupClassInstanceID.Eq(groupClassInstanceID)).
		Count()
	if err != nil {
		return 0, err
	}
	return count, err
}

func (r groupClassDetailRepository) BatchUpdateSelective(ctx context.Context, updates []dto.UpdateGroupClassDetailDTO) error {
	if len(updates) == 0 {
		return nil
	}
	for _, update := range updates {
		_, err := r.query.WithContext(ctx).GroupClassDetail.
			Where(r.query.GroupClassDetail.ID.Eq(update.GroupClassDetailID)).
			Updates(gormx.Update(GroupClassDetailConverter.UpdateToOpt(update)))
		if err != nil {
			return err
		}
	}
	return nil
}

func (r groupClassDetailRepository) GetByPetAndInstance(ctx context.Context, petID int64, instanceID int64) (*model.GroupClassDetail, error) {
	result, err := r.query.WithContext(ctx).GroupClassDetail.
		Where(r.query.GroupClassDetail.PetID.Eq(petID)).
		Where(r.query.GroupClassDetail.GroupClassInstanceID.Eq(instanceID)).Limit(1).Find()
	if err != nil {
		return nil, err
	}
	if len(result) == 0 {
		return nil, nil
	}
	return result[0], nil
}

func (r groupClassDetailRepository) ListByFilter(ctx context.Context, filter filter.ListGroupClassDetailFilter) ([]*model.GroupClassDetail, error) {
	// 确保索引可用
	if len(filter.IDs) == 0 && len(filter.FulfillmentIDs) == 0 && len(filter.GroupClassInstanceIDs) == 0 && len(filter.PetIDs) == 0 {
		return []*model.GroupClassDetail{}, nil
	}
	selectQuery := r.query.WithContext(ctx).GroupClassDetail.Select()
	if len(filter.IDs) > 0 {
		selectQuery.Where(r.query.GroupClassDetail.ID.In(filter.IDs...))
	}
	if len(filter.FulfillmentIDs) > 0 {
		selectQuery.Where(r.query.GroupClassDetail.FulfillmentID.In(filter.FulfillmentIDs...))
	}
	if len(filter.PetIDs) > 0 {
		selectQuery.Where(r.query.GroupClassDetail.PetID.In(filter.PetIDs...))
	}
	if len(filter.GroupClassIDs) > 0 {
		selectQuery.Where(r.query.GroupClassDetail.GroupClassID.In(filter.GroupClassIDs...))
	}
	if len(filter.Statuses) > 0 {
		selectQuery.Where(r.query.GroupClassDetail.Status.In(util.EnumSliceValuer(filter.Statuses)...))
	}
	if len(filter.GroupClassInstanceIDs) > 0 {
		selectQuery.Where(r.query.GroupClassDetail.GroupClassInstanceID.In(filter.GroupClassInstanceIDs...))
	}
	models, err := selectQuery.Find()
	if err != nil {
		return nil, err
	}
	return models, nil
}

func (r groupClassDetailRepository) WithQuery(q *query.Query) GroupClassDetailRepository {
	if q != nil {
		return &groupClassDetailRepository{query: q}
	}
	return &groupClassDetailRepository{query: r.query}
}

func (r groupClassDetailRepository) Create(ctx context.Context, model *model.GroupClassDetail) error {
	return r.query.WithContext(ctx).GroupClassDetail.Create(model)
}

func (r groupClassDetailRepository) Update(ctx context.Context, model *model.GroupClassDetail) error {
	_, err := r.query.WithContext(ctx).GroupClassDetail.Where(r.query.GroupClassDetail.ID.Eq(model.ID)).Updates(model)
	if err != nil {
		return err
	}
	return nil
}

func (r groupClassDetailRepository) GetByID(ctx context.Context, id int64) (*model.GroupClassDetail, error) {
	return r.query.WithContext(ctx).GroupClassDetail.Where(r.query.Fulfillment.ID.Eq(id)).First()
}

func (r groupClassDetailRepository) DeleteByPetAndInstance(ctx context.Context, petID int64, instanceID int64) error {
	_, err := r.query.WithContext(ctx).GroupClassDetail.
		Where(r.query.GroupClassDetail.PetID.Eq(petID)).
		Where(r.query.GroupClassDetail.GroupClassInstanceID.Eq(instanceID)).
		Delete()
	return err
}

func NewGroupClassDetailRepository(db *gorm.DB) GroupClassDetailRepository {
	return &groupClassDetailRepository{
		query: query.Use(db),
	}
}
