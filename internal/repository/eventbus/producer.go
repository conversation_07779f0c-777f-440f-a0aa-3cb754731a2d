package eventbus

import (
	"context"

	"github.com/google/uuid"
	"google.golang.org/protobuf/proto"

	eventbus "github.com/MoeGolibrary/go-lib/event-bus"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
)

type Producer interface {
	SendFulfillmentCanceledEvent(ctx context.Context, fulfillmentID int64, autoRefundOrder bool) error
}

type producer struct {
	eventbusProducer *eventbus.Producer[proto.Message]
}

func NewProducer() Producer {
	// For now, return a disabled producer to fix compilation
	// TODO: Implement proper eventbus configuration
	return &producer{}
}

func (p *producer) SendFulfillmentCanceledEvent(ctx context.Context, fulfillmentID int64, autoRefundOrder bool) error {
	if p.eventbusProducer == nil {
		// EventBus is disabled
		return nil
	}

	event := &eventbuspb.EventData{
		Event: &eventbuspb.EventData_FulfillmentCanceledEvent{
			FulfillmentCanceledEvent: &eventbuspb.FulfillmentCanceledEvent{
				Id:              fulfillmentID,
				AutoRefundOrder: autoRefundOrder,
			},
		},
	}

	return p.eventbusProducer.SendMessage(ctx, &eventbus.Event[proto.Message]{
		ID:        uuid.NewString(),
		Detail:    event,
		EventType: eventbuspb.EventType_FULFILLMENT_CANCELED,
	})
}
