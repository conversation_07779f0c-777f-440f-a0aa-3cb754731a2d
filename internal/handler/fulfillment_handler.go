package handler

import (
	"context"

	fulfillmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	fulfillmentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/fulfillment/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/converter/impl"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	"github.com/samber/lo"
)

var FulfillmentConverter = &impl.FulfillmentConverter{}
var GroupClassConverter = &impl.GroupClassConverter{}

type FulfillmentHandler struct {
	fulfillmentsvcpb.UnimplementedFulfillmentServiceServer
	service service.FulfillmentService
}

func (h FulfillmentHandler) EnrollPet(ctx context.Context, request *fulfillmentsvcpb.EnrollPetRequest) (*fulfillmentsvcpb.EnrollPetResponse, error) {
	enrollmentDTO := dto.PetEnrollmentDTO{
		CompanyID:            request.GetCompanyId(),
		BusinessID:           request.GetBusinessId(),
		PetID:                request.GetPetId(),
		GroupClassInstanceID: request.GetInstanceId(),
		Source:               request.GetSource(),
		StaffID:              request.StaffId,
	}
	orderID, err := h.service.EnrollPet(ctx, enrollmentDTO)
	if err != nil {
		return nil, err
	}
	return &fulfillmentsvcpb.EnrollPetResponse{
		OrderId: orderID,
	}, nil
}

func (h FulfillmentHandler) RemovePet(ctx context.Context, request *fulfillmentsvcpb.RemovePetRequest) (*fulfillmentsvcpb.RemovePetResponse, error) {
	_, err := h.service.RemovePet(ctx, request)
	if err != nil {
		return nil, err
	}
	return &fulfillmentsvcpb.RemovePetResponse{}, nil
}

func (h FulfillmentHandler) ListFulfillments(ctx context.Context, request *fulfillmentsvcpb.ListFulfillmentsRequest) (*fulfillmentsvcpb.ListFulfillmentsResponse, error) {
	fulfillmentsDTO := dto.ListFulfillmentsDTO{
		CompanyID:   request.GetCompanyId(),
		BusinessIDs: request.BusinessIds,
		GroupClassFilter: &dto.GroupClassDetailFilter{
			PetIDs:        request.GetFilter().GetPetIds(),
			GroupClassIDs: request.GetFilter().GetServiceIds(),
		},
		FulfillmentFilter: &dto.FulfillmentFilter{
			Statuses:    request.GetFilter().GetStatuses(),
			CustomerIds: request.GetFilter().GetCustomerIds(),
		},
		Pagination: request.GetPagination(),
	}
	fulfillments, pagination, err := h.service.ListFulfillments(ctx, fulfillmentsDTO)
	if err != nil {
		return nil, err
	}
	return &fulfillmentsvcpb.ListFulfillmentsResponse{
		Fulfillments: FulfillmentConverter.ModelsToPB(fulfillments),
		Pagination:   pagination,
	}, nil
}

func (h FulfillmentHandler) CreateFulfillment(ctx context.Context, request *fulfillmentsvcpb.CreateFulfillmentRequest) (*fulfillmentsvcpb.CreateFulfillmentResponse, error) {
	createDTO := FulfillmentConverter.CreatePBToDetailsDTO(request.Fulfillment)
	createDTO.GroupClasses = lo.Map(request.GetGroupClasses(), func(createDef *fulfillmentpb.GroupClassCreateDef, _ int) *dto.GroupClassDetailsDTO {
		return GroupClassConverter.CreatePBToDetailsDTO(createDef.GetGroupClassDetail())
	})

	fulfillmentID, _, err := h.service.CreateFulfillment(ctx, *createDTO)
	if err != nil {
		return nil, err
	}
	return &fulfillmentsvcpb.CreateFulfillmentResponse{
		FulfillmentId: fulfillmentID,
	}, nil
}

func (h FulfillmentHandler) GetFulfillment(ctx context.Context, request *fulfillmentsvcpb.GetFulfillmentRequest) (*fulfillmentsvcpb.GetFulfillmentResponse, error) {
	model, err := h.service.GetFulfillment(ctx, request.GetFulfillmentId())
	if err != nil {
		return nil, err
	}
	return &fulfillmentsvcpb.GetFulfillmentResponse{
		Fulfillment: FulfillmentConverter.ModelToPB(model),
	}, nil
}

func (h FulfillmentHandler) ExecuteCompensationTask(ctx context.Context, _ *fulfillmentsvcpb.ExecuteCompensationTaskRequest) (*fulfillmentsvcpb.ExecuteCompensationTaskResponse, error) {
	err := h.service.SyncFulfillmentStatus(ctx)
	if err != nil {
		return nil, err
	}
	return &fulfillmentsvcpb.ExecuteCompensationTaskResponse{}, nil
}

func NewFulfillmentHandler(fulfillmentService service.FulfillmentService) *FulfillmentHandler {
	return &FulfillmentHandler{
		service: fulfillmentService,
	}
}
