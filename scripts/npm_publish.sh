#!/usr/bin/env bash
# Note: no jq here

set -xeuo pipefail

TAG=latest
OLD_VERSION=$(node -p "require('./package.json').version")
MAJOR="$(echo "$OLD_VERSION" | cut -d. -f1)"

case "$BRANCH_NAME" in
production | main)
  export VERSION="$MAJOR.$GITHUB_RUN_ID.0"
  ;;
feature-* | bugfix-*)
  TAG=$(echo "$BRANCH_NAME" | sed -E 's/^(feature|bugfix)-(.*)$/\2/')
  CURRENT_DATE=$(date +%Y%m%d%H%M)
  FORMAT_BUILD_ID=$(printf '%03d' $GITHUB_RUN_ID)
  export VERSION="$OLD_VERSION-$TAG.$CURRENT_DATE$FORMAT_BUILD_ID"
  ;;
*)
  echo "Skip publish npm on branch $BRANCH_NAME"
  exit 0
  ;;
esac

npm-cli-login \
  -u "$NPM_PUBLISHER_USR" \
  -p "$NPM_PUBLISHER_PSW" \
  -e <EMAIL> \
  -r "https://nexus.devops.moego.pet/repository/npm-local"

for dir in web node; do
  cp -r "template/ts/$dir/". "tmp/$dir"
  sed -i "s/VERSION/$VERSION/g" "tmp/$dir/package.json"
  yarn tsc -p "tmp/$dir/tsconfig.json"
  # 当处理 Node 时, 移除 @ts-expect-error
  if [ "$dir" = "node" ]; then
    sed -i '/@ts-expect-error/d' "tmp/$dir/serviceClient.ts"
    sed -i '/@ts-expect-error/d' "tmp/$dir/serviceClient.js"
  fi
  echo "Publishing $dir..."
  pushd "tmp/$dir"
  npm publish --tag "$TAG"
  popd
done
