/**
 * generate service's client code
 * sync templates/node/application.yaml to templates/node/serviceClient.ts
 *
 * e.g. auto generate: 'export const ReportingServiceClient = createClient(ReportingService, createGrpcTransport({ baseUrl: 'http://moego-svc-reporting:9090' }))'
 */
import fs from 'fs';
import { load } from 'js-yaml';
import { globSync } from 'glob';
import { Project } from 'ts-morph';
import protoBuf from 'protobufjs';

const project = new Project();
const pwd = process.cwd();
const applicationYaml = `${pwd}/template/ts/node/application.yaml`;
const applicationYamlContent = fs.readFileSync(applicationYaml, 'utf8');
const config = load(applicationYamlContent);
const stubs = config.moego.grpc.client.stubs;
const serviceClientPath = `${pwd}/template/ts/node/serviceClient.ts`;

const pick = (obj, keys) => keys.reduce((acc, key) => (acc ? acc[key] : null), obj);

const serviceList = stubs
  // .filter((i) => i.service === 'moego.service.reporting.v2.*')
  .map((stub) => {
    const { service, authority } = stub;
    const serviceGlobPath = `${pwd}/${service.replace(/\./g, '/').replace('**', '')}*.proto`;
    const serviceFiles = globSync(serviceGlobPath);
    const serviceInfoList = serviceFiles.map((serviceFilePath) => {
      let serviceNames = [];
      const protoContent = fs.readFileSync(serviceFilePath, 'utf8');
      try {
        const result = protoBuf.parse(protoContent);
        const packageName = result.package;
        const keysPath = (packageName.replace(/\./g, '.nested.') + '.nested').split('.');
        const serviceObj = pick(result.root.nested, keysPath);
        if (serviceObj) {
          serviceNames = Object.keys(serviceObj)
            .map((key) => {
              const serviceName = serviceObj[key];
              const isService = !!serviceName.methods;
              return isService ? key : null;
            })
            .filter(Boolean);
        }
        return {
          serviceFilePath,
          serviceNames,
        };
      } catch (error) {
        console.error(`Error parsing proto file: ${serviceFilePath}`, error.message || error);
        return {
          serviceFilePath,
          serviceNames: [],
        };
      }
    }).filter((i) => i.serviceNames.length > 0);
    return {
      serviceInfoList,
      authority,
    };
  })
  .filter((i) => i.serviceInfoList.length > 0);

const sourceFile = project.createSourceFile(serviceClientPath, '', { overwrite: true });
sourceFile.addImportDeclaration({
  namedImports: ['createClient'],
  moduleSpecifier: '@connectrpc/connect',
  leadingTrivia: '// @ts-expect-error',
});
sourceFile.addImportDeclaration({
  namedImports: ['createGrpcTransport'],
  moduleSpecifier: '@connectrpc/connect-node',
  leadingTrivia: '// @ts-expect-error',
});

const ConflictIdentifier = {};

for (const item of serviceList) {
  const { serviceInfoList, authority } = item;
  const baseUrl = `http://${authority}`;
  for (const serviceInfo of serviceInfoList) {
    const { serviceFilePath, serviceNames } = serviceInfo;
    const relativePath = serviceFilePath.replace(pwd, '.');
    const isV2 = relativePath.includes('v2');
    const importPath = relativePath.replace('.proto', '_pb');
    console.log(`importPath: ${importPath}`);
    // extract service module name(e.g. reporting) from importPath, e.g. /moego/service/reporting/v2 ~~ reporting
    const matchResult = importPath.match(/(?:moego\/service|backend\/proto)\/(.*?)\/.*/);
    if (!matchResult || !matchResult[1]) {
      throw new Error(
        `Invalid path: ${serviceFilePath}, you should confirm the service path. e.g. xxx/moego/service/reporting/v1/xxx.proto`,
      );
    }
    /** e.g. subscription, online_booking */
    const serviceModuleName = matchResult[1];
    for (const serviceName of serviceNames) {
      let identifier = isV2 ? `${serviceName}V2` : serviceName;
      const key = `${serviceModuleName}.${serviceName}`;
      if (ConflictIdentifier[key]) {
        console.warn(
          `Conflict identifier ${identifier} for service ${serviceName} in module ${serviceModuleName}.`,
        );
        continue;
      }
      ConflictIdentifier[key] = true;
      const useAlias = serviceName !== identifier;
      sourceFile.addImportDeclaration({
        namedImports: useAlias ? [{ name: serviceName, alias: identifier }] : [identifier],
        moduleSpecifier: importPath,
      });
      const clientName = `${identifier}Client`;
      sourceFile.addVariableStatement({
        declarationKind: 'const',
        isExported: true,
        declarations: [
          {
            name: clientName,
            initializer: `createClient(${identifier}, createGrpcTransport({ baseUrl: '${baseUrl}' }))`,
          },
        ],
      });
    }
  }
}

sourceFile.saveSync();
