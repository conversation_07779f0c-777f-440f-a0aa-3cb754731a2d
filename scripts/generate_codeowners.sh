#!/bin/bash

# Purpose: 自动生成CODEOWNERS文件
# 功能：
# 1. 扫描所有app的metadata.yaml文件
# 2. 提取codeowners配置
# 3. 生成根目录的CODEOWNERS文件

# <AUTHOR> <EMAIL>

source scripts/common.sh

BACKEND_APP_DIR='backend/app'
CODEOWNERS_FILE='CODEOWNERS'
TEMP_CODEOWNERS_FILE='.temp_codeowners'

echo -e "${YELLOW}generate CODEOWNERS file...${NC}"

cat > ${TEMP_CODEOWNERS_FILE} << 'EOF'
# This file is auto-generated by scripts/generate_codeowners.sh
# Do not edit this file manually. Edit metadata.yaml files in each app directory instead.
# 
# 格式说明：
# /path/to/directory/ @owner1 @owner2
# 表示该目录下的所有文件都由指定的所有者管理

EOF

for app_dir in ${BACKEND_APP_DIR}/*/; do
    if [ -d "$app_dir" ]; then
        app_name=$(basename "$app_dir")
        metadata_file="${app_dir}metadata.yaml"
        
        if [ -f "$metadata_file" ]; then
            codeowners=""
            if command -v yq &> /dev/null; then
                codeowners=$(yq eval '.spec.codeowners[]?' "$metadata_file" 2>/dev/null | tr '\n' ' ')
            else
                if grep -q "codeowners:" "$metadata_file"; then
                codeowners=$(awk '/codeowners:/,/^[^ ]/ {if ($0 ~ /- /) {gsub(/[[:space:]]*- /, ""); gsub(/"/, ""); printf "%s ", $0}}' "$metadata_file")
                fi
            fi
            
            if [ ! -z "$codeowners" ]; then
                echo "# Code owners for ${app_name} service" >> ${TEMP_CODEOWNERS_FILE}
                echo "/${BACKEND_APP_DIR}/${app_name}/ $codeowners" >> ${TEMP_CODEOWNERS_FILE}
                echo "" >> ${TEMP_CODEOWNERS_FILE}
                echo -e "${GREEN}✓ set CODEOWNERS for ${app_name}: $codeowners${NC}"
            fi
        else
            echo -e "${RED}✗ ${app_name} missing metadata.yaml file${NC}"
        fi
    fi
done

echo "# Default code owners for everything else" >> ${TEMP_CODEOWNERS_FILE}
echo "* <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL>" >> ${TEMP_CODEOWNERS_FILE}

mv ${TEMP_CODEOWNERS_FILE} ${CODEOWNERS_FILE}
echo -e "${GREEN}CODEOWNERS file generated ${CHECK}${NC}"