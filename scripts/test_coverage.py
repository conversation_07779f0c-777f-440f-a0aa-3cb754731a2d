#!/usr/bin/env python3

import subprocess
import re
import os
import sys
import platform
from typing import Dict, List, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class CoverageConfig:
    """覆盖率配置类"""
    min_coverage: int
    exclude_dirs: List[str]
    project_requirements: Dict[str, int]

class CoverageChecker:
    def __init__(self, coverage_config: CoverageConfig):
        self.config = coverage_config
        self.bazel_command = self._get_bazel_command()
        self.bazel_output_path = self._get_bazel_output_path()
        
    def _get_bazel_command(self) -> str:
        """获取bazel命令"""
        bazel_dir = "./bazel"
        if platform.system() == "Darwin":
            return f"bazelisk --bazelrc={bazel_dir}/mac.bazelrc"
        return f"bazelisk --bazelrc={bazel_dir}/.bazelrc"
        
    def _get_bazel_output_path(self) -> str:
        """获取bazel输出路径"""
        try:
            result = subprocess.run(self.bazel_command.split() + ['info', 'output_path'], 
                                  capture_output=True, text=True, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"获取bazel输出路径失败: {e}")
            sys.exit(1)
    
    def _get_changed_files(self) -> List[str]:
        """获取变更的文件列表"""
        try:
            # 确定比较的目标
            current_branch = subprocess.run(['git', 'rev-parse', '--abbrev-ref', 'HEAD'],
                                         capture_output=True, text=True, check=True).stdout.strip()
            target = 'origin/main' if current_branch != 'main' else 'HEAD^'
            
            # 获取变更的文件
            diff_files = subprocess.run(['git', 'diff', target, '--name-only', '--diff-filter=ACMR',
                                      'backend/', 'backend/*.go', 'backend/*.proto', 'backend/*.yaml'],
                                     capture_output=True, text=True, check=True).stdout.splitlines()
            
            # 获取未跟踪的文件
            untracked_files = subprocess.run(['git', 'ls-files', '--others', '--exclude-standard',
                                           'backend/', 'backend/*.go', 'backend/*.proto', 'backend/*.yaml'],
                                          capture_output=True, text=True, check=True).stdout.splitlines()
            
            # 合并并去重
            all_files = set(diff_files + untracked_files)
            return sorted(list(all_files))
        except subprocess.CalledProcessError as e:
            print(f"获取变更文件失败: {e}")
            sys.exit(1)
    
    def get_changed_apps(self) -> List[str]:
        """获取变更的应用目录"""
        try:
            changed_files = self._get_changed_files()
            if not changed_files:
                print("没有backend目录的变更")
                return []
            
            # 提取应用目录并去重
            app_dirs = set()
            for file_path in changed_files:
                match = re.search(r'backend/app/([^/]+)/', file_path)
                if match:
                    app_name = match.group(1)
                    if app_name not in self.config.exclude_dirs:
                        app_dirs.add(f"//backend/app/{app_name}/logic/...")
            
            return list(app_dirs)
        except Exception as e:
            print(f"处理变更文件失败: {e}")
            sys.exit(1)
    
    def run_coverage(self, app_dirs: List[str]) -> None:
        """运行覆盖率测试"""
        if not app_dirs:
            print("没有需要测试的应用目录")
            return
            
        try:
            bazel_cmd = self.bazel_command.split() + ['coverage', '--combined_report=lcov'] + app_dirs
            print(f"执行命令: {' '.join(bazel_cmd)}")
            subprocess.run(bazel_cmd, check=True)
        except subprocess.CalledProcessError as e:
            print(f"执行bazel coverage失败: {e}")
            print("请确保:")
            print("1. bazelisk已正确安装")
            print("2. 项目已正确配置bazel")
            print("3. 测试目标存在且可执行")
            sys.exit(1)
    
    def get_coverage_summary(self) -> Dict[str, float]:
        """获取覆盖率摘要"""
        coverage_file = os.path.join(self.bazel_output_path, '_coverage', '_coverage_report.dat')
        try:
            result = subprocess.run(['lcov', '--summary', coverage_file], 
                                  capture_output=True, text=True, check=True)
            
            print("覆盖率报告内容:")
            print(result.stdout)
            
            summary = {}
            for line in result.stdout.split('\n'):
                if 'lines' in line:
                    match = re.search(r'(\d+\.\d+)', line)
                    if match:
                        summary['lines'] = float(match.group(1))
                    else:
                        print(f"警告: 无法从行中提取行覆盖率: {line}")
                        summary['lines'] = 0.0
                elif 'functions' in line:
                    match = re.search(r'(\d+\.\d+)', line)
                    if match:
                        summary['functions'] = float(match.group(1))
                    else:
                        print(f"警告: 无法从行中提取函数覆盖率: {line}")
                        summary['functions'] = 0.0
            
            if 'lines' not in summary:
                print("错误: 未找到行覆盖率数据")
                summary['lines'] = 0.0
            if 'functions' not in summary:
                print("错误: 未找到函数覆盖率数据")
                summary['functions'] = 0.0
                
            return summary
        except subprocess.CalledProcessError as e:
            print(f"获取覆盖率摘要失败: {e}")
            print(f"命令输出: {e.stdout if e.stdout else '无输出'}")
            print(f"错误输出: {e.stderr if e.stderr else '无错误'}")
            sys.exit(1)
    
    def generate_html_report(self) -> None:
        """生成HTML报告"""
        coverage_file = os.path.join(self.bazel_output_path, '_coverage', '_coverage_report.dat')
        output_dir = 'bazel/test-report'
        try:
            subprocess.run(['genhtml', '--branch-coverage', '--output', output_dir, coverage_file],
                          check=True)
            print(f"HTML报告已生成，请查看: {output_dir}/index.html")
        except subprocess.CalledProcessError as e:
            print(f"生成HTML报告失败: {e}")
            sys.exit(1)
    
    def check_coverage(self, app_dirs: List[str]) -> bool:
        """检查覆盖率是否满足要求"""
        if not app_dirs:
            return True
            
        self.run_coverage(app_dirs)
        coverage = self.get_coverage_summary()
        
        for app_dir in app_dirs:
            # 从路径中提取项目名称
            project_match = re.search(r'/app/([^/]+)/', app_dir)
            if not project_match:
                continue
                
            project_name = project_match.group(1)
            required_coverage = self.config.project_requirements.get(
                project_name, 
                self.config.project_requirements['default']
            )
            
            print(f"检查项目 {project_name} 的覆盖率:")
            print(f"当前行覆盖率: {coverage['lines']:.2f}%")
            print(f"要求覆盖率: {required_coverage}%")
            
            if coverage['lines'] < required_coverage:
                print(f"错误: {project_name} 的覆盖率低于要求")
                return False
        
        return True

def main():
    # 配置覆盖率要求
    coverage_config = CoverageConfig(
        min_coverage=80,
        exclude_dirs=['temp'],
        project_requirements={
            'default': 80,
            'user': 85,
            'order': 90,
            'payment': 95
        }
    )
    
    # 解析命令行参数
    generate_html = len(sys.argv) > 1 and sys.argv[1] == '--html'
    
    checker = CoverageChecker(coverage_config)
    app_dirs = checker.get_changed_apps()
    
    if not app_dirs:
        print("没有变更的应用，跳过覆盖率检查")
        return
    
    if checker.check_coverage(app_dirs):
        print("所有项目的覆盖率检查通过")
        if generate_html:
            checker.generate_html_report()
    else:
        print("覆盖率检查失败")
        sys.exit(1)

if __name__ == '__main__':
    main() 