#!/bin/bash

# Purpose: 脚本用于获取某次提交时变更的文件, 只做分析操作, 不执行任何后续命令
# Maintainer: <EMAIL>

source scripts/common.sh
set -euo pipefail
CACHE_FILE=$(mktemp)
# 统一文件模式定义
declare -a FILE_PATTERNS_KEYS=("backend" "frontend")
declare -a FILE_PATTERNS_VALUES=(
    '*.go *.proto *.yaml'
    '*.tsx *.ts *.js *.html *.css *.json *.yaml'
)

# 添加使用说明
show_help() {
    echo "Usage: $0 [backend|frontend]"
    echo "Output: space-separated list of changed apps"
}


# 获取所有变更的文件列表
contrast_target() {
    # 确定比较的目标
    local TARGET="origin/main"
    [[ "$CURRENT_BRANCH" == "main" ]] && TARGET="HEAD^"
    
    # 动态生成路径参数
    local patterns=()
    for dir_type in "${FILE_PATTERNS_KEYS[@]}"; do
        patterns_str=""
        for i in "${!FILE_PATTERNS_KEYS[@]}"; do
        if [[ "${FILE_PATTERNS_KEYS[i]}" == "$dir_type" ]]; then
            patterns_str="${FILE_PATTERNS_VALUES[i]}"
            break
        fi
    done

    patterns+=("${dir_type}/")
    for ext in $patterns_str; do
        patterns+=("${dir_type}/$ext")
        done
    done

    # 合并执行git命令
    git diff "$TARGET" --name-only --diff-filter=ACMR -- ${patterns[@]}
    git ls-files --others --exclude-standard -- ${patterns[@]}
}

# 获取所有变更的文件列表
get_changed_files() {
    local changedFiles=()
    while IFS= read -r file; do
        [[ -n "$file" ]] && changedFiles+=("$file")
    done < <(contrast_target | sort -u)

    echo "${changedFiles[@]}"
}

get_changed_apps() {
    local dir="$1"
    local changedApps=()
    
    # 使用mapfile提升读取效率
    mapfile -t -d '' files < <(tr '\n' '\0' < "$CACHE_FILE")
    
    for file in "${files[@]}"; do
        [[ "$file" =~ ^$dir/app/([^/]+)/ ]] && changedApps+=("${BASH_REMATCH[1]}")
    done

    # 使用关联数组去重
    local -A unique_apps=()
    for app in "${changedApps[@]}"; do
        unique_apps["$app"]=1
    done
    
    # 修正数组长度检查
    if (( ${#unique_apps[@]} > 0 )); then
        printf '%s ' "${!unique_apps[@]}" | sed 's/ $//'
    fi
}

changed_apps() {
    local dir="${1:-backend}"
    if [[ ! "$dir" =~ ^(backend|frontend)$ ]]; then
        show_help >&2
        return 1  # 改为return避免退出父shell
    fi
    trap 'rm -f "$CACHE_FILE"' EXIT
    contrast_target | sort -u > "$CACHE_FILE"

    local result
    result=$(get_changed_apps "$dir")
    echo "$result"  # 无论结果是否为空都输出
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "$(changed_apps "$@")"
fi