#!/bin/bash
## color
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color
CHECK="✓"
CROSS="✗"
STAR=✨

# Setup git safe directory if in a container environment
if [ -n "${GITHUB_ACTIONS:-}" ] || [ -n "${CI:-}" ]; then
    git config --global --add safe.directory "$(pwd)" 2>/dev/null || true
fi

# command
BAZEL_DIR="./bazel"
BAZEL_COMMAND="bazelisk --bazelrc=${BAZEL_DIR}/.bazelrc"
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
MIN_BAZELISK_VERSION="1.24.1"
MIN_GO_VERSION="1.24"

