diff --git a/node_modules/ts-proto/build/src/generate-services.js b/node_modules/ts-proto/build/src/generate-services.js
index bba3120..061023d 100644
--- a/node_modules/ts-proto/build/src/generate-services.js
+++ b/node_modules/ts-proto/build/src/generate-services.js
@@ -25,7 +25,8 @@ function generateService(ctx, fileDesc, sourceInfo, serviceDesc) {
     const chunks = [];
     (0, utils_1.maybeAddComment)(options, sourceInfo, chunks, serviceDesc.options?.deprecated);
     const maybeTypeVar = options.context ? `<${main_1.contextTypeVar}>` : "";
-    chunks.push((0, ts_poet_1.code) `export interface ${(0, ts_poet_1.def)(serviceDesc.name)}${maybeTypeVar} {`);
+    chunks.push((0, ts_poet_1.code) `export interface ${(0, ts_poet_1.def)(serviceDesc.name)}${maybeTypeVar} {
+      $fullName: '${(0, utils_1.maybePrefixPackage)(fileDesc, serviceDesc.name)}';`);
     serviceDesc.method.forEach((methodDesc, index) => {
         (0, utils_1.assertInstanceOf)(methodDesc, utils_1.FormattedMethodDescriptor);
         const info = sourceInfo.lookup(sourceInfo_1.Fields.service.method, index);
diff --git a/node_modules/ts-proto/build/src/main.js b/node_modules/ts-proto/build/src/main.js
index eeb9fe5..90085a2 100644
--- a/node_modules/ts-proto/build/src/main.js
+++ b/node_modules/ts-proto/build/src/main.js
@@ -841,7 +841,7 @@ function generateInterfaceDeclaration(ctx, fullName, messageDesc, sourceInfo, fu
         (0, utils_1.maybeAddComment)(options, info, chunks, fieldDesc.options?.deprecated);
         const fieldKey = (0, utils_1.safeAccessor)((0, utils_1.getFieldName)(fieldDesc, options));
         const isOptional = (0, types_1.isOptionalProperty)(fieldDesc, messageDesc.options, options, currentFile.isProto3Syntax);
-        const type = (0, types_1.toTypeName)(ctx, messageDesc, fieldDesc, isOptional);
+        const type = (0, types_1.toTypeName)(ctx, messageDesc, fieldDesc, false);
         chunks.push((0, ts_poet_1.code) `${maybeReadonly(options)}${fieldKey}${isOptional ? "?" : ""}: ${type}, `);
     });
     if (ctx.options.unknownFields) {
diff --git a/node_modules/ts-proto/build/src/options.js b/node_modules/ts-proto/build/src/options.js
index 2bdb03a..c536ce4 100644
--- a/node_modules/ts-proto/build/src/options.js
+++ b/node_modules/ts-proto/build/src/options.js
@@ -216,7 +216,7 @@ function optionsFromParameter(parameter) {
         }
         else {
             // useJsonWireFormat implies stringEnums=true and useDate=string
-            options.stringEnums = true;
+            // options.stringEnums = true;
             options.useDate = DateOption.STRING;
         }
     }
diff --git a/node_modules/ts-proto/build/src/types.js b/node_modules/ts-proto/build/src/types.js
index f190766..5d1cb9d 100644
--- a/node_modules/ts-proto/build/src/types.js
+++ b/node_modules/ts-proto/build/src/types.js
@@ -542,7 +542,7 @@ function valueTypeName(ctx, typeName) {
                     ? (0, ts_poet_1.code) `readonly string[]`
                     : (0, ts_poet_1.code) `string[]`;
         case ".google.protobuf.Duration":
-            return ctx.options.useJsonWireFormat ? (0, ts_poet_1.code) `string` : undefined;
+            return ctx.options.useJsonWireFormat || true ? (0, ts_poet_1.code) `string` : undefined;
         case ".google.protobuf.Timestamp":
             return ctx.options.useJsonWireFormat ? (0, ts_poet_1.code) `string` : undefined;
         default:
@@ -671,9 +671,7 @@ function toTypeName(ctx, messageDesc, field, ensureOptional = false) {
     // clause, spelling each option out inside a large type union. No need for
     // union with `undefined` here, either.
     const { options } = ctx;
-    return finalize(type, (!isWithinOneOf(field) &&
-        isMessage(field) &&
-        (options.useOptionals === false || options.useOptionals === "none")) ||
+    return finalize(type,
         (isWithinOneOf(field) && options.oneof === options_1.OneofOption.PROPERTIES) ||
         (isWithinOneOf(field) && field.proto3Optional) ||
         ensureOptional);
diff --git a/node_modules/ts-proto/build/src/utils.js b/node_modules/ts-proto/build/src/utils.js
index ca69619..30840a1 100644
--- a/node_modules/ts-proto/build/src/utils.js
+++ b/node_modules/ts-proto/build/src/utils.js
@@ -187,8 +187,8 @@ class FormattedMethodDescriptor {
     static formatName(methodName, options) {
         let result = methodName;
         if (options.lowerCaseServiceMethods || options.outputServices.includes(options_1.ServiceOption.GRPC)) {
-            if (options.snakeToCamel)
-                result = (0, case_1.camelCaseGrpc)(result);
+            // 只处理首字母
+            result = result.substring(0, 1).toLowerCase() + result.substring(1);
         }
         return result;
     }
