// @since 2025-03-31 15:46:22
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.fulfillment.v1;

import "moego/models/fulfillment/v1/fulfillment_defs.proto";
import "moego/models/fulfillment/v1/fulfillment_enums.proto";
import "moego/models/fulfillment/v1/fulfillment_models.proto";
import "moego/models/fulfillment/v1/group_class_defs.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/fulfillment/v1;fulfillmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.fulfillment.v1";

// The request for enrolling a pet into a group class instance
message EnrollPetRequest {
  // The business ID
  int64 business_id = 1 [(validate.rules).int64.gt = 0];

  // The training group class instance ID
  int64 instance_id = 2 [(validate.rules).int64.gt = 0];

  // The pet's ID
  int64 pet_id = 3 [(validate.rules).int64.gt = 0];

  // The company ID
  int64 company_id = 4 [(validate.rules).int64.gt = 0];

  // The fulfillment source
  models.fulfillment.v1.Source source = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // The enrolled staff
  optional int64 staff_id = 6 [(validate.rules).int64.gt = 0];
}

// The response for enrolling a pet into a group class instance
message EnrollPetResponse {
  // The order ID
  int64 order_id = 1;
  // The fulfillment ID
  int64 fulfillment_id = 2;
}

// The request for removing a pet from a group class instance
message RemovePetRequest {
  // The company ID, optional
  optional int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // The training group class instance ID
  int64 instance_id = 2 [(validate.rules).int64.gt = 0];
  // The pet's ID
  int64 pet_id = 3 [(validate.rules).int64.gt = 0];
  // Whether to auto refund the order, default is false
  bool auto_refund_order = 4;
}

// The response for removing a pet from a group class instance
message RemovePetResponse {
  // The order ID associated with the removed pet
  int64 order_id = 1;
}

// The request for ListFulfillments
message ListFulfillmentsRequest {
  // The company ID
  int64 company_id = 1 [(validate.rules).int64.gt = 0];

  // The business ID
  repeated int64 business_ids = 2 [(validate.rules).repeated = {
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // The filter
  Filter filter = 3;

  // The pagination
  optional moego.utils.v2.PaginationRequest pagination = 4;

  // The filter
  message Filter {
    // The pet id
    repeated int64 pet_ids = 1 [(validate.rules).repeated = {
      max_items: 100
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];

    // The service id
    repeated int64 service_ids = 2 [(validate.rules).repeated = {
      max_items: 100
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];

    // The customer ids
    repeated int64 customer_ids = 3 [(validate.rules).repeated = {
      max_items: 100
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];

    // The status
    repeated models.fulfillment.v1.Status statuses = 4 [(validate.rules).repeated = {
      max_items: 100
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // The fulfillment id
    repeated int64 fulfillment_ids = 5 [(validate.rules).repeated = {
      max_items: 100
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];
  }
}

// The response for ListFulfillments
message ListFulfillmentsResponse {
  // The fulfillment list
  repeated moego.models.fulfillment.v1.FulfillmentModel fulfillments = 1;

  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// The request for CreateFulfillment
message CreateFulfillmentRequest {
  // The fulfillment detail
  moego.models.fulfillment.v1.FulfillmentCreateDef fulfillment = 1;

  // Group class create detail
  repeated moego.models.fulfillment.v1.GroupClassCreateDef group_classes = 2;
}

// The response for CreateFulfillment
message CreateFulfillmentResponse {
  // The fulfillment ID
  int64 fulfillment_id = 1;
}

// The request for GetFulfillment
message GetFulfillmentRequest {
  // The fulfillment ID
  int64 fulfillment_id = 1 [(validate.rules).int64.gt = 0];
}

// The response for GetFulfillment
message GetFulfillmentResponse {
  // The fulfillment detail
  moego.models.fulfillment.v1.FulfillmentModel fulfillment = 1;
}

// The message for UpdateFulfillment
message UpdateFulfillmentRequest {
  // The fulfillment update def
  moego.models.fulfillment.v1.FulfillmentUpdateDef fulfillment = 2;
}

// The message for UpdateFulfillment
message UpdateFulfillmentResponse {}

// The request for ExecuteCompensationTask
message ExecuteCompensationTaskRequest {}

// The response for ExecuteCompensationTask
message ExecuteCompensationTaskResponse {}

// the fulfillment service
service FulfillmentService {
  // Enroll a pet into a group class instance
  rpc EnrollPet(EnrollPetRequest) returns (EnrollPetResponse);

  // Remove a pet from a group class instance
  rpc RemovePet(RemovePetRequest) returns (RemovePetResponse);

  // List fulfillment by filter
  rpc ListFulfillments(ListFulfillmentsRequest) returns (ListFulfillmentsResponse);

  // Create fulfillment
  rpc CreateFulfillment(CreateFulfillmentRequest) returns (CreateFulfillmentResponse);

  // Get fulfillment by id
  rpc GetFulfillment(GetFulfillmentRequest) returns (GetFulfillmentResponse);

  // Update fulfillment by id
  rpc UpdateFulfillment(UpdateFulfillmentRequest) returns (UpdateFulfillmentResponse);

  // Execute compensation task, sync fulfillment status pending_payment to unconfirmed
  rpc ExecuteCompensationTask(ExecuteCompensationTaskRequest) returns (ExecuteCompensationTaskResponse);
}
