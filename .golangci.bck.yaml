run:
  timeout: 3m
  allow-parallel-runners: true

issues:
  exclude-dirs:
    - backend/test/api_integration
    - backend/common/rpc
    - cli/devops_executor/cmd
    - template/template-go
    - tmp
    - backend/app/voice_agent

output:
  sort-results: true

linters-settings:
  funlen:
    lines: 80
    statements: 80
  goconst:
    min-len: 2
    min-occurrences: 2
  gocyclo:
    min-complexity: 10
  # govet: # deprecated
  #   check-shadowing: true
  lll:
    line-length: 120
    tab-width: 4
  errcheck:
    check-type-assertions: true
  gocritic:
    enabled-checks:
      - nestingReduce
    settings:
      nestingReduce:
        bodyWidth: 5
  staticcheck:     # staticcheck 规则文档: https://staticcheck.io/docs/checks/
    checks:
      - all
      - '-SA1019'

linters:
  enable:
    ## enabled by default
    - errcheck # checking for unchecked errors, these unchecked errors can be critical bugs in some cases
    - gosimple # specializes in simplifying a code
    - govet # reports suspicious constructs, such as Printf calls whose arguments do not align with the format string
    - ineffassign # detects when assignments to existing variables are not used
    - staticcheck # is a go vet on steroids, applying a ton of static analysis checks
    - typecheck # like the front-end of a Go compiler, parses and type-checks Go code
    - unused # checks for unused constants, variables, functions and types
    - goconst # finds repeated strings that could be replaced by a constant
    - revive # fast, configurable, extensible, flexible, and beautiful linter for Go, drop-in replacement of golint
    - lll # reports long lines
    - funlen # tool for detection of long functions
  disable:
    ## disabled by default
    - asasalint # checks for pass []any as any in variadic func(...any)
    - asciicheck # checks that your code does not contain non-ASCII identifiers
    - bidichk # checks for dangerous unicode character sequences
    - bodyclose # checks whether HTTP response body is closed successfully
    - cyclop # checks function and package cyclomatic complexity
    - dupl # tool for code clone detection
    - durationcheck # checks for two durations multiplied together
    - errname # checks that sentinel errors are prefixed with the Err and error types are suffixed with the Error
    - errorlint # finds code that will cause problems with the error wrapping scheme introduced in Go 1.13
    - exhaustive # checks exhaustiveness of enum switch statements
    - forbidigo # forbids identifiers
    - gocheckcompilerdirectives # validates go compiler directive comments (//go:)
    - gochecknoglobals # checks that no global variables exist
    - gochecknoinits # checks that no init functions are present in Go code
    - gochecksumtype # checks exhaustiveness on Go "sum types"
    - gocognit # computes and checks the cognitive complexity of functions
    - gocyclo # computes and checks the cyclomatic complexity of functions
    - gocritic # provides diagnostics that check for bugs, performance and style issues
    - godot # checks if comments end in a period
    - goimports # in addition to fixing imports, goimports also formats your code in the same style as gofmt
    - gomoddirectives # manages the use of 'replace', 'retract', and 'excludes' directives in go.mod
    - gomodguard # allow and block lists linter for direct Go module dependencies. This is different from depguard where there are different block types for example version constraints and module recommendations
    - goprintffuncname # checks that printf-like functions are named with f at the end
    - loggercheck # checks key value pairs for common logger libraries (kitlog,klog,logr,zap)
    - makezero # finds slice declarations with non-zero initial length
    - mirror # reports wrong mirror patterns of bytes/strings usage
    - musttag # enforces field tags in (un)marshaled structs
    - mnd # detects magic numbers
    - nakedret # finds naked returns in functions greater than a specified function length
    - nestif # reports deeply nested if statements
    - nilerr # finds the code that returns nil even if it checks that the error is not nil
    - nilnil # checks that there is no simultaneous return of nil error and an invalid value
    - noctx # finds sending http request without context.Context
    - nolintlint # reports ill-formed or insufficient nolint directives
    - nonamedreturns # reports all named returns
    - nosprintfhostport # checks for misuse of Sprintf to construct a host with port in a URL
    - perfsprint # checks that fmt.Sprintf can be replaced with a faster alternative
    - predeclared # finds code that shadows one of Go's predeclared identifiers
    - promlinter # checks Prometheus metrics naming via promlint
    - protogetter # reports direct reads from proto message fields when getters should be used
    - reassign # checks that package variables are not reassigned
    - rowserrcheck # checks whether Err of rows is checked successfully
    - sloglint # ensure consistent code style when using log/slog
    - spancheck # checks for mistakes with OpenTelemetry/Census spans
    - sqlclosecheck # checks that sql.Rows and sql.Stmt are closed
    - stylecheck # is a replacement for golint
    - tenv # detects using os.Setenv instead of t.Setenv since Go1.17
    - testableexamples # checks if examples are testable (have an expected output)
    - testifylint # checks usage of github.com/stretchr/testify
    - testpackage # makes you use a separate _test package
    - tparallel # detects inappropriate usage of t.Parallel() method in your Go e2e codes
    - unconvert # removes unnecessary type conversions
    - unparam # reports unused function parameters
    - usestdlibvars # detects the possibility to use variables/constants from the Go standard library
    - wastedassign # finds wasted assignment statements
    - whitespace # detects leading and trailing whitespace
    - gosec # inspects source code for security problems
