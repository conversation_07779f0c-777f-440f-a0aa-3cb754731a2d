version: v2
plugins:
  # - remote: buf.build/community/pseudomuto-doc:v1.5.1
  #   out: gen/doc
  #   opt: html,index.html,source_relative
  - remote: buf.build/protocolbuffers/go
    out: tmp/go
    opt: paths=source_relative
  - remote: buf.build/grpc/go
    out: tmp/go
    opt:
      - paths=source_relative
      - require_unimplemented_servers=true
  - local: protoc-gen-es
    out: ./tmp/node
    opt: 
      - target=ts
    include_wkt: true
    include_imports: true
  - local: ./node_modules/ts-proto/protoc-gen-ts_proto
    out: tmp/web
    strategy: all
    opt:
      - globalThisPolyfill=false
      - context=false
      - forceLong=string
      - esModuleInterop=true
      - env=browser
      - useOptionals=none
      - exportCommonSymbols=false
      - unrecognizedEnum=false
      - removeEnumPrefix=false
      - lowerCaseServiceMethods=true
      - snakeToCamel=true
      - outputEncodeMethods=false
      - outputJsonMethods=false
      - outputPartialMethods=false
      - stringEnums=false
      - outputClientImpl=false
      - returnObservable=false
      - addGrpcMetadata=false
      - addNestjsRestParameter=false
      - nestJs=false
      - useDate=string
      - useMongoObjectId=string
      - outputSchema=false
      - outputTypeAnnotations=false
      - outputTypeRegistry=false
      - useAbortSignal=false
      - useAsyncIterable=false
      - emitImportedFiles=true
      - enumsAsLiterals=false
      - useExactTypes=true
      - unknownFields=false
      - onlyTypes=true
      - usePrototypeForDefaults=false
      - useJsonWireFormat=true
      - useNumericEnumForJson=true
      - initializeFieldsAsUndefined=false
      - useMapType=false
      - useReadonlyTypes=false
      - useSnakeTypeName=false
      - outputExtensions=false
      - outputIndex=false
