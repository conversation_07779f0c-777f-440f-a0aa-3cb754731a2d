load("@buildifier_prebuilt//:rules.bzl", "buildifier")
load("@gazelle//:def.bzl", "gazelle")

exports_files(
    ["buf.yaml"],
    visibility = ["//visibility:public"],
)
# gazelle:prefix github.com/MoeGolibrary/moego
# gazelle:go_grpc_compilers	@io_bazel_rules_go//proto:go_proto,@io_bazel_rules_go//proto:go_grpc_v2
# gazelle:exclude **/*.pb.validate.go
# gazelle:exclude _java_
# gazelle:exclude build

##

gazelle(
    name = "gazelle",
    # gazelle = ":gazelle-buf",
    prefix = "github.com/MoeGolibrary/moego",
)

gazelle(
    name = "gazelle-update-repos",
    args = [
        "-from_file=go.mod",
        "-to_macro=deps.bzl%go_dependencies",
        "-build_file_proto_mode=disable",
        "-prune",
    ],
    command = "update-repos",
    # gazelle = ":gazelle-buf",
)

buildifier(
    name = "buildifier.check",
    exclude_patterns = [
        "./.git/*",
    ],
    lint_mode = "warn",
    mode = "diff",
)

buildifier(
    name = "buildifier.fix",
    exclude_patterns = [
        "./.git/*",
    ],
    lint_mode = "fix",
    mode = "fix",
)
