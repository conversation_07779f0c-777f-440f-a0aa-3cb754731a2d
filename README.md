# MoeGo Monorepo

欢迎加入 MoeGo！这是一个基于 Bazel 构建的 Go 微服务单体仓库。

## 📚 目录

- [快速开始](#快速开始)
- [开发环境](#开发环境)
- [项目结构](#项目结构)
- [日常开发](#日常开发)
- [编译与测试](#编译与测试)
- [创建新项目](#创建新项目)
- [代码提交](#代码提交)
- [故障排除](#故障排除)
- [快速参考](#快速参考)
- [为什么选择 Bazel](#为什么选择-bazel)

## 🚀 快速开始

### 环境要求

> Ubuntu 用户请参考 [Ubuntu 安装指南](./docs/README_ubuntu.md)

**必需依赖：**
1. **Golang ≥ 1.24.0**
   ```bash
   brew install golang
   ```

2. **Git 配置**
   ```bash
   # 确保以下环境变量已设置
   export GOPRIVATE=github.com/MoeGolibrary
   
   # 确保 git 配置正确（token、邮箱、用户名）
   git config --global user.email "<EMAIL>"
   git config --global user.name "Your Name"
   ```

**自动安装的依赖：**
以下工具会在 `make init` 时自动安装：
- Bazelisk ≥ 1.24.1（构建工具）
- buf（Proto 文件管理）
- lcov（测试覆盖率报告）
- golangci-lint（代码检查）
- goimports-reviser（代码格式化）

### 一键初始化

```bash
# 克隆仓库后执行
make init
```

`make init` 会自动完成：
- ✅ 安装所有必需工具
- ✅ 配置 Git hooks
- ✅ 初始化 API 测试环境
- ✅ 更新所有 BUILD.bazel 文件

### 验证安装

```bash
# 编译所有项目验证环境
make build

# 运行示例项目
make run app=helloworld
```


### 开发工具配置

**Go 环境变量：**
```bash
# 添加到 ~/.zshrc 或 ~/.bashrc
export GOPRIVATE=github.com/MoeGolibrary
export GOPROXY=https://goproxy.cn,direct
export GO111MODULE=on
```

**Git Hooks：**
项目自动配置了以下 hooks：
- `pre-commit`: 运行代码检查
- `commit-msg`: 验证提交信息格式

## 📁 项目结构

```
moego/
├── backend/                 # 后端服务
│   ├── app/                 # 各个微服务
│   │   ├── customer/        # 客户服务
│   │   ├── sales/           # 销售服务
│   │   ├── pet/             # 宠物服务
│   │   └── ...
│   ├── common/              # 公共库
│   │   ├── rpc/             # RPC 框架
│   │   └── utils/           # 工具库
│   ├── proto/               # Protocol Buffers 定义
│   ├── test/                # 集成测试
│   └── tools/               # 开发工具
├── frontend/                # 前端项目
├── cli/                     # 命令行工具
├── docs/                    # 文档
├── scripts/                 # 构建脚本
└── template/                # 项目模板
```

## 🔄 日常开发

### 在已有项目上开发新 API

以 `pet` 服务为例：

**1. 定义 API**
```bash
# 编辑 proto 文件
vim backend/proto/pet/v1/pet.proto
```

```protobuf
service PetService {
  // 已有的 API...
  
  // 新增你的 API
  rpc CreatePet(CreatePetRequest) returns (CreatePetResponse);
}

// 新增 request/response 结构
message CreatePetRequest {
  string name = 1;
  string breed = 2;
}

message CreatePetResponse {
  string pet_id = 1;
}
```

**2. 编译 Proto 文件**
```bash
make proto
```

**3. 实现业务逻辑**
```bash
# 编辑服务实现
vim backend/app/pet/service/pet_service.go
```

**4. 测试和验证**
```bash
# 运行代码检查
make lint

# 编译项目
make build dir=//backend/app/pet

# 运行服务
make run app=pet local=1
```

### 常用开发命令

```bash
# 管理依赖（修改 go.mod 后必执行）
make gazelle

# 代码检查和格式化
make lint

# 编译指定服务
make build dir=//backend/app/pet

# 编译所有服务
make build

# 运行测试
make test dir=//backend/app/pet

# 获取变更的服务列表
make diff
```

## 🔨 编译与测试

### 编译项目

```bash
# 编译所有后端服务
make build

# 编译指定服务
make build dir=//backend/app/customer

# 编译指定目录下所有项目
make build dir=//backend/app/customer/...

# 编译 Proto 文件
make proto
```

### 运行服务

```bash
# 使用测试配置运行
make run app=customer

# 使用本地配置运行
make run app=customer local=1
```

### 测试

```bash
# 运行所有测试
make test

# 运行指定服务测试
make test dir=//backend/app/customer

# 生成测试报告
make test report=1
```

## 🆕 创建新项目

### 1. 注册模块

编辑 [`backend/.modules.yml`](./backend/.modules.yml)：

```yaml
modules:
  - name: your_module      # 模块名（如：platform、foundation）
    desc: 模块描述
    owner: your_email      # 负责人邮箱
    code: 20              # 唯一错误码段（10-99）
```

### 2. 创建项目

```bash
make create module=your_module service=your_service
```

这会自动：
- ✅ 复制项目模板
- ✅ 生成 Proto 文件
- ✅ 分配唯一错误码段
- ✅ 更新 BUILD.bazel 文件
- ✅ 设置 CODEOWNERS

### 3. 完善项目

手动完成以下步骤：

```bash
# 1. 配置项目
vim backend/app/your_service/config/*/config.yaml

# 2. 编写项目文档
vim backend/app/your_service/README.md

# 3. 编译 Proto（如果需要）
make proto

# 4. 开始编码！
vim backend/app/your_service/logic/...
```

## 📤 代码提交

### 提交前检查清单

```bash
# 1. 代码检查（必须通过）
make lint

# 2. 依赖管理（如果修改了 go.mod）
make gazelle

# 3. 编译验证
make build

# 4. 运行测试
make test
```

### Lint 检查包含

- ✅ **Proto 文件**：API 规范检查
- ✅ **Go 代码**：golangci-lint 检查
- ✅ **代码格式**：goimports-reviser 格式化
- ✅ **Bazel 文件**：buildifier 格式化
- ✅ **依赖管理**：go mod tidy

### CI/CD 流程

提交代码后，GitHub Actions 会自动：

1. **代码检查**：运行 `make lint`
2. **编译测试**：编译变更的服务
3. **单元测试**：运行相关测试
4. **集成测试**：API 集成测试
5. **自动部署**：部署到测试环境

## 性能优化

**编译性能：**
```bash
# 使用更多并行任务
export BAZEL_JOBS=$(nproc)  # Linux
export BAZEL_JOBS=$(sysctl -n hw.ncpu)  # macOS

# 启用本地缓存
echo "build --disk_cache=~/.cache/bazel" >> ~/.bazelrc
```

**内存使用：**
```bash
# 限制 Bazel 内存使用
echo "startup --host_jvm_args=-Xmx4g" >> ~/.bazelrc
```

### 安全注意事项

1. **不要提交敏感信息**：
   - 密码、Token、API 密钥
   - 本地配置文件（已在 .gitignore 中）

2. **使用环境变量**：
   ```bash
   # 在本地配置中使用环境变量
   database_url: ${DATABASE_URL}
   api_key: ${API_KEY}
   ```

3. **定期更新依赖**：
   ```bash
   # 检查过期依赖
   go list -u -m all
   
   # 更新依赖
   go get -u ./...
   ```

## ⚡ 快速参考

### 一键命令

| 命令 | 作用 |
|-----|------|
| `make init` | 初始化项目环境 |
| `make build` | 编译所有服务 |
| `make lint` | 代码检查和格式化 |
| `make test` | 运行所有测试 |
| `make proto` | 编译 Proto 文件 |
| `make clean` | 清理编译缓存 |
| `make gazelle` | 更新 BUILD 文件 |

### 目录快速跳转






- 服务配置：`backend/app/{service}/config/local/config.yaml`
- Proto 定义：`backend/proto/{service}/v1/{service}.proto`
- 业务逻辑：`backend/app/{service}/logic/`
- 数据访问：`backend/app/{service}/repo/`
- 服务实现：`backend/app/{service}/service/`

## 🤔 为什么选择 Bazel

详细说明请参考：[Bazel 使用指南](./docs/USE_BAZEL.md)

### 核心优势

- **🚀 构建速度**：增量编译 + 并行构建 + 分布式缓存
- **🌍 跨语言支持**：统一管理 Go、Proto、TypeScript 等
- **🔒 构建一致性**：沙箱编译，避免环境差异
- **📈 可扩展性**：支持超大型 monorepo

---

## 📞 获取帮助

- **技术问题**：在 [GitHub Issues](https://github.com/MoeGolibrary/moego/issues) 提问
- **紧急支持**：联系对应模块负责人（见 [.modules.yml](./backend/.modules.yml)）
- **文档问题**：提交 PR 改进文档

## 📖 更多文档

- [Monorepo 介绍](./docs/WHATSMONOREPO.md)
- [Bazel 详细指南](./docs/USE_BAZEL.md)
- [Ubuntu 安装指南](./docs/README_ubuntu.md)
- [API 测试说明](./backend/test/api_integration/README.md)
- [CODEOWNERS 管理](./docs/CODEOWNERS_MANAGEMENT.md)
- [错误处理指南](./backend/common/rpc/framework/errs/README.md)

---

🎉 **Happy Coding!**

<!-- Verification comment added by Devin to test repository access and PR workflow -->
