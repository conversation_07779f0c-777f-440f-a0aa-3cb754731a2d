version: v2
modules:
  - path: .
    excludes:
      - backend/proto/template
      - backend/proto/deps
      - bazel/out
      - bazel/test-report
      - node_modules
      - _java_
      - build
deps:
  - buf.build/bufbuild/protovalidate
  - buf.build/googleapis/googleapis
lint:
  use:
    # - COMMENT_ENUM
    # - COMMENT_ENUM_VALUE
    # - COMMENT_FIELD
    # - COMMENT_MESSAGE
    # - COMMENT_ONEOF
    # - COMMENT_RPC
    # - COMMENT_SERVICE
    - STANDARD
    # - PACKAGE_NO_IMPORT_CYCLE
  except:
    - ENUM_VALUE_PREFIX
    - FIELD_NOT_REQUIRED
    - PACKAGE_DIRECTORY_MATCH
    - RPC_REQUEST_RESPONSE_UNIQUE
    - RPC_REQUEST_STANDARD_NAME
    - ENUM_ZERO_VALUE_SUFFIX
  ignore:
    - bazel/out
    - bazel/test-report
    - backend/proto/deps
    - node_modules/  # 添加这行来忽略 node_modules 中的 proto 文件
    - _java_/
    - build/
