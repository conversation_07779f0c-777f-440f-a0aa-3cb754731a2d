{"rules": [{"rulePriority": 1, "description": "keep default images (last 3)", "selection": {"tagStatus": "tagged", "tagPrefixList": ["production", "staging", "master", "actions-production", "actions-staging", "actions-master"], "countType": "imageCountMoreThan", "countNumber": 3}, "action": {"type": "expire"}}, {"rulePriority": 2, "description": "purge hot images (last 6)", "selection": {"tagStatus": "tagged", "tagPrefixList": ["hot-", "actions-hot-"], "countType": "imageCountMoreThan", "countNumber": 6}, "action": {"type": "expire"}}, {"rulePriority": 3, "description": "purge gate images (last 2)", "selection": {"tagStatus": "tagged", "tagPrefixList": ["gate-", "actions-gate-"], "countType": "imageCountMoreThan", "countNumber": 2}, "action": {"type": "expire"}}, {"rulePriority": 4, "description": "purge pro images (last 2)", "selection": {"tagStatus": "tagged", "tagPrefixList": ["pro-", "actions-pro-"], "countType": "imageCountMoreThan", "countNumber": 2}, "action": {"type": "expire"}}, {"rulePriority": 5, "description": "prune dev images (keep 7d)", "selection": {"tagStatus": "tagged", "tagPrefixList": ["dev-", "actions-dev-"], "countType": "sinceImagePushed", "countUnit": "days", "countNumber": 7}, "action": {"type": "expire"}}, {"rulePriority": 6, "description": "prune untagged images", "selection": {"tagStatus": "untagged", "countType": "imageCountMoreThan", "countNumber": 1}, "action": {"type": "expire"}}]}