package random

import (
	"strings"

	"github.com/google/uuid"
)

// 字符集常量定义
const (
	// 大写字母和非零数字(排除数字0)
	UpperLetterNZ = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	LowerLetterNZ = "abcdefghijklmnopqrstuvwxyz"
	UpperNumberNZ = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789"
	LowerNumberNZ = "abcdefghijklmnopqrstuvwxyz123456789"
	AllNumberNZ   = "0123456789"
	AllLetterNZ   = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
	AllNZ         = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz123456789"
)

// UpperString 随机生成只包含大写字母+数字的字符串
// 例如: A28UOT (length = 6)
// @param length 随机字符串的长度
// @return 长度为 length 的字符串
func UpperString(length int) string {
	if length <= 0 {
		return ""
	}
	// 返回指定长度的字符串
	uuid := String(length)
	return strings.ToUpper(uuid)
}

// String 生成随机字符串
func String(length int) string {
	if length <= 0 {
		return ""
	}
	uuid := uuid.NewString()
	uuid = strings.ReplaceAll(uuid, "-", "")
	return uuid[:length]
}
