package pagination

import (
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type Pagination struct {
	Offset  int
	Limit   int
	OrderBy *string
}

func (p *Pagination) Validate() error {
	if p.Offset < 0 || p.Limit <= 0 {
		return status.Errorf(codes.InvalidArgument, "invalid pagination")
	}
	return nil
}

func NewPagination(page int, pageSize int) *Pagination {
	return &Pagination{
		Offset: (page - 1) * pageSize,
		Limit:  pageSize,
	}
}
