load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "struct",
    srcs = ["struct.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/utils/struct",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_bytedance_sonic//:sonic",
        "@io_gorm_datatypes//:datatypes",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/structpb",
    ],
)
