package structutil

import (
	"encoding/json"
	"reflect"

	"github.com/bytedance/sonic"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"
	"gorm.io/datatypes"
)

// ErrNilValue 表示传入的值为nil
var errNilValue = status.Error(codes.InvalidArgument, "input value is nil")

// ConvertToProtoStruct 将任意结构体转换为 structpb.Struct
// 支持的类型:
// - 结构体
// - 结构体指针
// - map[string]interface{}
// - 可以被json序列化的类型
//
// 返回:
// - *structpb.Struct: 转换后的结构
// - error: 转换过程中的错误
func ConvertToProtoStruct(v any) (*structpb.Struct, error) {
	// 检查输入是否为nil
	if v == nil {
		return nil, errNilValue
	}

	// 检查类型
	val := reflect.ValueOf(v)
	if val.Kind() == reflect.Ptr && val.IsNil() {
		return nil, errNilValue
	}

	// 如果已经是map[string]any类型，直接转换
	if m, ok := v.(map[string]any); ok {
		return structpb.NewStruct(m)
	}

	// 1. 先将结构体转换为 JSON
	jsonBytes, err := json.Marshal(v)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "failed to marshal to JSON: %v", err)
	}

	// 2. 将 JSON 解析为 map[string]any
	var rawMap map[string]any
	if err := json.Unmarshal(jsonBytes, &rawMap); err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "failed to unmarshal JSON to map: %v", err)
	}

	// 检查map是否为空
	if len(rawMap) == 0 {
		// 返回空的Struct
		return structpb.NewStruct(map[string]any{})
	}

	// 3. 将 map 转换为 structpb.Struct
	protoStruct, err := structpb.NewStruct(rawMap)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "failed to convert map to structpb.Struct: %v", err)
	}

	return protoStruct, nil
}

// ConvertProtoStructToJSON 将 structpb.Struct 转换为 datatypes.JSON
// 支持的类型:
// - *structpb.Struct: protobuf 结构体
// - nil: 返回空的 JSON
//
// 返回:
// - datatypes.JSON: 转换后的 JSON 数据
// - error: 转换过程中的错误
func ConvertProtoStructToJSON(protoStruct *structpb.Struct) (datatypes.JSON, error) {
	// 检查输入是否为nil
	if protoStruct == nil {
		return datatypes.JSON("{}"), nil
	}

	// 将 structpb.Struct 转换为 map[string]interface{}
	rawMap := protoStruct.AsMap()

	// 将 map 转换为 JSON 字节
	jsonBytes, err := sonic.Marshal(rawMap)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "failed to marshal map to JSON: %v", err)
	}

	// 创建 datatypes.JSON
	jsonData := datatypes.JSON(jsonBytes)
	return jsonData, nil
}

// ConvertJSONToProtoStruct 将 datatypes.JSON 转换为 structpb.Struct
// 支持的类型:
// - datatypes.JSON: GORM JSON 类型
//
// ..
// 返回:
// - *structpb.Struct: 转换后的 protobuf 结构体
// - error: 转换过程中的错误
func ConvertJSONToProtoStruct(jsonData datatypes.JSON) (*structpb.Struct, error) {
	// 检查输入是否为空
	if len(jsonData) == 0 {
		return structpb.NewStruct(map[string]interface{}{})
	}

	// 将 datatypes.JSON 解析为 map[string]interface{}
	var rawMap map[string]interface{}
	if err := json.Unmarshal(jsonData, &rawMap); err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "failed to unmarshal JSON: %v", err)
	}

	// 将 map 转换为 structpb.Struct
	protoStruct, err := structpb.NewStruct(rawMap)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "failed to convert map to structpb.Struct: %v", err)
	}

	return protoStruct, nil
}
