package money

// copy from https://github.com/MoeGolibrary/go-lib/blob/main/common/proto/money/money.go
import (
	"github.com/shopspring/decimal"
	"google.golang.org/genproto/googleapis/type/money"
)

const (
	nano     = 1000 * 1000 * 1000
	moneyExp = -9
)

var nanoDecimal = decimal.New(nano, 0)

func NewDecimal(units int64, nanos int32) decimal.Decimal {
	return decimal.New(units*nano+int64(nanos), moneyExp)
}

func ToDecimal(m *money.Money) decimal.Decimal {
	return decimal.New(m.GetUnits()*nano+int64(m.GetNanos()), moneyExp)
}

func FromDecimal(d decimal.Decimal, code string) *money.Money {
	return &money.Money{
		CurrencyCode: code,
		Units:        d.IntPart(),
		Nanos:        int32(d.Mul(nanoDecimal).Mod(nanoDecimal).IntPart()),
	}
}

func ToFloat(m *money.Money) float64 {
	return ToDecimal(m).InexactFloat64()
}

func FromFloat(f float64, code string) *money.Money {
	return FromDecimal(decimal.NewFromFloat(f), code)
}
