package money

import (
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/genproto/googleapis/type/money"
)

// copy from https://github.com/MoeGolibrary/go-lib/blob/main/common/proto/money/money.go
func TestDecimal(t *testing.T) {
	m := &money.Money{
		Units: 1,
		Nanos: 0.75 * nano,
	}
	require.Equal(t, 1.75, ToDecimal(m).InexactFloat64())
	d := NewDecimal(1, 0.75*nano)
	require.Equal(t, int64(1), FromDecimal(d, "USD").GetUnits())
	require.Equal(t, int32(0.75*nano), FromDecimal(d, "USD").GetNanos())
}

func TestFloat(t *testing.T) {
	m := FromFloat(1.2345, "USD")
	require.Equal(t, int64(1), m.GetUnits())
	require.Equal(t, int32(0.2345*nano), m.<PERSON><PERSON><PERSON>())
	require.Equal(t, ToFloat(m), 1.2345)
}
