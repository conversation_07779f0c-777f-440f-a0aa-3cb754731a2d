package grpc

import (
	"context"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport"
)

func init() {
	transport.RegisterClientTransport("grpc", DefaultClientTransport)
}

var DefaultClientTransport = &clientTransport{}

type clientTransport struct {
	connectionPool pool
}

func (c *clientTransport) RoundTrip(ctx context.Context,
	_ []byte, roundTripOpts ...transport.RoundTripOption) (rsp []byte, err error) {
	header := GetHeader(ctx)
	reqBody := header.Req
	rspBody := header.Rsp

	opts := &transport.RoundTripOptions{}
	for _, o := range roundTripOpts {
		o(opts)
	}

	msg := codec.Message(ctx)
	ctx = setGRPCMetaData(ctx, msg)

	md := &metadata.MD{}
	var callOpts []grpc.CallOption
	callOpts = append(callOpts, grpc.Header(md))

	conn, err := c.connectionPool.Get(opts.Address)
	if err != nil {
		return nil, errs.NewFrameError(codes.Canceled, "connect fail: "+err.Error())
	}
	if err = conn.Invoke(ctx, msg.ClientRPCName(),
		reqBody, rspBody, callOpts...); err != nil {
		return nil, errs.NewFrameError(codes.Internal, "grpc invoke fail: "+err.Error())
	}
	header.InMetaData = *md

	return nil, nil
}

func setGRPCMetaData(ctx context.Context, msg codec.Msg) context.Context { // TODO: metadata in msg
	header := GetHeader(ctx)
	var kv []string
	for k, vals := range header.OutMetaData {
		for _, v := range vals {
			kv = append(kv, k, v)
		}
	}
	for k, vals := range msg.ClientMetaData() {
		for _, v := range vals {
			kv = append(kv, k, v)
		}
	}
	if kv != nil {
		ctx = metadata.AppendToOutgoingContext(ctx, kv...)
	}

	return ctx
}
