load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "recovery",
    srcs = ["recovery.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/filter",
        "//backend/common/rpc/framework/log",
        "@org_golang_google_grpc//codes",
    ],
)
