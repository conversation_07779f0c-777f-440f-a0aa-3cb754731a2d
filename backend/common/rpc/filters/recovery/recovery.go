package recovery

import (
	"context"
	"fmt"
	"runtime"

	"google.golang.org/grpc/codes"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/filter"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func init() {
	filter.Register("recovery", ServerFilter(), nil)
}

// PanicBufLen is the size of the buffer for storing the panic call stack log. The default value as below.
var PanicBufLen = 4096

type options struct {
	rh Handler
}

// Option sets Recovery option.
type Option func(*options)

// Handler is the Recovery handle function.
type Handler func(ctx context.Context, err interface{}) error

// WithRecoveryHandler sets Recovery handle function.
func WithRecoveryHandler(rh Handler) Option {
	return func(opts *options) {
		opts.rh = rh
	}
}

var defaultRecoveryHandler = func(ctx context.Context, e interface{}) error {
	buf := make([]byte, PanicBufLen)
	buf = buf[:runtime.Stack(buf, false)]
	log.ErrorContextf(ctx, "[PANIC]%v\n%s\n", e, buf)
	return errs.NewFrameError(codes.Internal, fmt.Sprint(e))
}

var defaultOptions = &options{
	rh: defaultRecoveryHandler,
}

// ServerFilter adds the recovery filter to the server.
func ServerFilter(opts ...Option) filter.ServerFilter {
	o := defaultOptions
	for _, opt := range opts {
		opt(o)
	}
	return func(ctx context.Context, req interface{}, handler filter.ServerHandleFunc) (rsp interface{}, err error) {
		defer func() {
			if r := recover(); r != nil {
				err = o.rh(ctx, r)
			}
		}()

		return handler(ctx, req)
	}
}
