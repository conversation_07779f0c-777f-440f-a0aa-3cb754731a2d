package debuglog

import (
	"context"
	"fmt"
	"time"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/filter"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func init() {
	filter.Register("debuglog", ServerFilter(), ClientFilter())
}

// DefaultLogFunc is the default struct print method.
var DefaultLogFunc = func(_ context.Context, req, rsp interface{}) string {
	return fmt.Sprintf(", req:%+v, rsp:%+v", req, rsp)
}

// ServerFilter is the server-side filter.
func ServerFilter() filter.ServerFilter {
	return func(ctx context.Context, req interface{}, handler filter.ServerHandleFunc) (rsp interface{}, err error) {
		begin := time.Now()
		rsp, err = handler(ctx, req)
		msg := codec.Message(ctx)

		end := time.Now()
		var addr string
		if msg.RemoteAddr() != nil {
			addr = msg.RemoteAddr().String()
		}
		if err == nil {
			log.DebugContextf(ctx, "%s rcv a request from %s, cost:%s%s",
				msg.CalleeMethod(), addr, end.Sub(begin), DefaultLogFunc(ctx, req, rsp))
		} else {
			deadline, ok := ctx.Deadline()
			if ok {
				log.ErrorContextf(ctx, "%s rcv a request from %s, cost:%s, err:%s, total timeout:%s%s",
					msg.CalleeMethod(), addr, end.Sub(begin), err.Error(), deadline.Sub(begin),
					DefaultLogFunc(ctx, req, rsp))
			} else {
				log.ErrorContextf(ctx, "%s rcv a request from %s, cost:%s, err:%s%s",
					msg.CalleeMethod(), addr, end.Sub(begin), err.Error(), DefaultLogFunc(ctx, req, rsp))
			}
		}
		return rsp, err
	}
}

func ClientFilter() filter.ClientFilter {
	return func(ctx context.Context, req, rsp interface{}, handler filter.ClientHandleFunc) (err error) {
		msg := codec.Message(ctx)
		begin := time.Now()
		err = handler(ctx, req, rsp)
		end := time.Now()
		var addr string
		if msg.RemoteAddr() != nil {
			addr = msg.RemoteAddr().String()
		}
		if err == nil {
			log.DebugContextf(ctx, "client request: %s, cost:%s, to:%s%s",
				msg.ClientRPCName(), end.Sub(begin), addr, DefaultLogFunc(ctx, req, rsp))
		} else {
			log.DebugContextf(ctx, "client request: %s, cost:%s, to:%s, err:%s%s",
				msg.ClientRPCName(), end.Sub(begin), addr, err.Error(), DefaultLogFunc(ctx, req, rsp))
		}
		return err
	}
}
