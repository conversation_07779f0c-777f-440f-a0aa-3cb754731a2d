load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "debuglog",
    srcs = ["log.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/codec",
        "//backend/common/rpc/framework/filter",
        "//backend/common/rpc/framework/log",
    ],
)
