package logs

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/filter"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

const (
	requestIDKey = "x-request-id"
	logKey       = "id"
)

func ServerFilter() filter.ServerFilter {
	return func(ctx context.Context, req interface{}, handle filter.ServerHandleFunc) (rsp interface{}, err error) {
		meta := codec.Message(ctx).ServerMetaData()
		if meta == nil {
			return handle(ctx, req)
		}

		var fields []log.Field
		ids, ok := meta[requestIDKey]
		if ok && len(ids) > 0 {
			fields = append(fields, []log.Field{
				{Key: requestIDKey, Value: ids[0]},
				{Key: logKey, Value: ids[0]},
			}...)
		}
		log.WithContextFields(ctx, fields...)
		return handle(ctx, req)
	}
}

func ClientFilter() filter.ClientFilter {
	return func(ctx context.Context, req interface{}, rsp interface{}, handle filter.ClientHandleFunc) (err error) {
		return handle(ctx, req, rsp)
	}
}
