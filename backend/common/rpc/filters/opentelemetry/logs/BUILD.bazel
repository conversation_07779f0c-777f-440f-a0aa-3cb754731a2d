load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "logs",
    srcs = [
        "flow.go",
        "logs.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry/logs",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/codec",
        "//backend/common/rpc/framework/filter",
        "//backend/common/rpc/framework/log",
        "@io_opentelemetry_go_otel//attribute",
        "@io_opentelemetry_go_otel_trace//:trace",
    ],
)
