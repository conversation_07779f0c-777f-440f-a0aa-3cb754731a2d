package traces

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	ddotel "github.com/DataDog/dd-trace-go/v2/ddtrace/opentelemetry"
	ddtracer "github.com/DataDog/dd-trace-go/v2/ddtrace/tracer"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/baggage"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/propagation"
	semconv "go.opentelemetry.io/otel/semconv/v1.32.0"
	"go.opentelemetry.io/otel/trace"

	"github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry/logs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/filter"
	mhttp "github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

var (
	defaultTracer     trace.Tracer
	defaultTracerOnce sync.Once
)

func getDefaultTracer() trace.Tracer {
	defaultTracerOnce.Do(func() {
		defaultTracer = otel.Tracer("")
	})
	return defaultTracer
}

func Setup(options ...SetupOption) error {
	for _, v := range options {
		v(defaultSetupOptions)
	}
	provider := ddotel.NewTracerProvider(defaultSetupOptions.ddOptions()...)
	otel.SetTracerProvider(provider)
	otel.SetTextMapPropagator(
		propagation.NewCompositeTextMapPropagator(
			propagation.TraceContext{},
			propagation.Baggage{},
		),
	)
	return nil
}

// SetupOption opentelemetry setup option
type SetupOption func(*setupOptions)

type setupOptions struct {
	serviceName string
	env         string
	version     string
}

func (o *setupOptions) ddOptions() []ddtracer.StartOption {
	return []ddtracer.StartOption{
		ddtracer.WithService(o.serviceName),
		ddtracer.WithEnv(o.env),
		ddtracer.WithServiceVersion(o.version),
	}
}

var defaultSetupOptions = &setupOptions{
	serviceName: os.Getenv("DD_SERVICE"),
	env:         os.Getenv("DD_ENV"),
	version:     os.Getenv("DD_VERSION"),
}

// FilterOptions FilterOptions
type FilterOptions struct {
	// DisableTraceBody disable req/rsp event log
	DisableTraceBody bool
	// DisableParentSampling ignore parent sampling
	DisableParentSampling bool
}

// FilterOption filter option
type FilterOption func(*FilterOptions)

var defaultFilterOptions = FilterOptions{
	DisableTraceBody:      false,
	DisableParentSampling: false,
}

// ServerFilter opentelemetry server filter in trpc
func ServerFilter(opts ...FilterOption) filter.ServerFilter {
	opt := defaultFilterOptions
	for _, v := range opts {
		v(&opt)
	}
	return func(ctx context.Context, req interface{}, f filter.ServerHandleFunc) (rsp interface{}, err error) {
		start := time.Now()
		msg := codec.Message(ctx)
		md := msg.ServerMetaData()
		if md == nil {
			md = codec.MetaData{}
		}
		ctx, span := startServerSpan(ctx, req, msg, md, opt)
		defer span.End()

		log.WithContextFields(ctx,
			log.Field{Key: "dd.trace_id", Value: span.SpanContext().TraceID().String()},
			log.Field{Key: "dd.span_id", Value: convertSpanID(span.SpanContext().SpanID().String())},
			log.Field{Key: "dd.service", Value: defaultSetupOptions.serviceName},
			log.Field{Key: "dd.env", Value: defaultSetupOptions.env},
			log.Field{Key: "dd.version", Value: defaultSetupOptions.version},
		)

		receivedDeadline := getDeadline(ctx)
		receivedTime := time.Now()

		rsp, err = f(ctx, req)

		sentDeadline := getDeadline(ctx)
		sentTime := time.Now()

		var code int
		codeStr, err1 := GetDefaultGetCodeFunc()(ctx, rsp, err)
		if c, e := strconv.Atoi(codeStr); e == nil {
			code = c
		}
		flow := buildFlowLog(msg, trace.SpanKindServer)
		handleError(code, err1, span, flow)
		if needToTraceBody(span, opt, err1) {
			flow.Request.Body = addEvent(ctx, req, semconv.RPCMessageTypeReceived, receivedDeadline, receivedTime)
			flow.Response.Body = addEvent(ctx, rsp, semconv.RPCMessageTypeSent, sentDeadline, sentTime)
		}

		span.SetAttributes(DefaultAttributesAfterServerHandle(ctx, rsp)...)
		flow.Cost = time.Since(start).String()
		return rsp, err
	}
}

func convertSpanID(id string) string {
	if len(id) < 16 {
		return ""
	}
	if len(id) > 16 {
		id = id[16:]
	}
	intValue, err := strconv.ParseUint(id, 16, 64)
	if err != nil {
		return ""
	}
	return strconv.FormatUint(intValue, 10)
}

func buildFlowLog(msg codec.Msg, kind trace.SpanKind) *logs.FlowLog {
	var sourceAddr, targetAddr string
	if msg.RemoteAddr() != nil {
		targetAddr = msg.RemoteAddr().String()
	}
	if msg.LocalAddr() != nil {
		sourceAddr = msg.LocalAddr().String()
	}

	if kind == trace.SpanKindServer {
		sourceAddr, targetAddr = targetAddr, sourceAddr
	}

	flow := &logs.FlowLog{
		Kind: logs.FlowKind(kind),
		Source: logs.Service{
			Name:      msg.CallerServiceName(),
			Method:    msg.CallerMethod(),
			Namespace: msg.EnvName(),
			Address:   sourceAddr,
		},
		Target: logs.Service{
			Name:      msg.CalleeServiceName(),
			Method:    msg.CalleeMethod(),
			Address:   targetAddr,
			Namespace: msg.EnvName(),
		},
	}

	return flow
}

const (
	SpanKindClient = "spanKindClient"
	SpanKindServer = "spanKindServer"
)

func startServerSpan(ctx context.Context,
	req interface{}, msg codec.Msg, md codec.MetaData, opt FilterOptions) (context.Context, trace.Span) {

	suppliers := GetTextMapCarriers(md, msg)
	ctx = otel.GetTextMapPropagator().Extract(ctx, suppliers)
	spanContext := trace.SpanContextFromContext(ctx)

	spanKind := trace.SpanKindServer
	if kind, ok := msg.CommonMeta()[SpanKindServer].(trace.SpanKind); ok {
		spanKind = kind
	}

	spanStartOptions := []trace.SpanStartOption{
		trace.WithSpanKind(spanKind),
		trace.WithAttributes(peerInfo(msg.RemoteAddr())...),
		trace.WithAttributes(hostInfo(msg.LocalAddr())...),
		trace.WithAttributes(CallerServiceKey.String(msg.CallerServiceName())),
		trace.WithAttributes(CallerMethodKey.String(msg.CallerMethod())),
		trace.WithAttributes(CalleeServiceKey.String(msg.CalleeServiceName())),
		trace.WithAttributes(CalleeMethodKey.String(msg.CalleeMethod())),
		trace.WithAttributes(DefaultTraceAttributesFunc(ctx, req)...),
		trace.WithAttributes(BaggageHeader.String(baggage.FromContext(ctx).String())),
	}

	if opt.DisableParentSampling {
		spanContext = spanContext.WithTraceFlags(spanContext.TraceFlags() &^ trace.FlagsSampled)
	}

	return getDefaultTracer().Start(
		trace.ContextWithRemoteSpanContext(ctx, spanContext),
		msg.ServerRPCName(),
		spanStartOptions...)
}

func needToTraceBody(span trace.Span, opt FilterOptions, err error) bool {
	if opt.DisableTraceBody {
		return false
	}

	if span.SpanContext().IsSampled() {
		return true
	}
	return err != nil
}

func handleError(errCode int, err error, span trace.Span, flow *logs.FlowLog) {
	code, msg, errType := getErrCode(errCode, err)
	if code != 0 {
		span.SetStatus(codes.Error, msg)
	} else {
		span.SetStatus(codes.Ok, msg)
	}

	span.SetAttributes(
		StatusCode.Int64(int64(code)),
		StatusMsg.String(msg),
		StatusType.Int(errType),
	)
	flow.Status = logs.Status{
		Code:    int32(code),
		Message: msg,
		Type:    toErrorType(errType),
	}
}

func getErrCode(errCode int, err error) (int, string, int) {
	if err == nil {
		return errCode, "", errs.ErrorTypeUnknown
	}
	return int(errs.Code(err)), errs.Msg(err), errs.Type(err)
}

// ClientFilter client filter in trpc
func ClientFilter(opts ...FilterOption) filter.ClientFilter {
	opt := defaultFilterOptions
	for _, v := range opts {
		v(&opt)
	}
	return func(ctx context.Context, req interface{}, rsp interface{}, f filter.ClientHandleFunc) error {
		start := time.Now()
		msg := codec.Message(ctx)
		md := msg.ClientMetaData()
		if md == nil {
			md = codec.MetaData{}
		}

		suppliers := GetTextMapCarriers(md, msg)
		ctx, span := startClientSpan(ctx, req, msg)
		defer span.End()

		otel.GetTextMapPropagator().Inject(ctx, suppliers)
		msg.WithClientMetaData(md)

		sentDeadline := getDeadline(ctx)
		sentTime := time.Now()
		err := f(ctx, req, rsp)
		receivedDeadline := getDeadline(ctx)
		receivedTime := time.Now()
		var code int
		codeStr, err1 := GetDefaultGetCodeFunc()(ctx, rsp, err)
		if c, e := strconv.Atoi(codeStr); e == nil {
			code = c
		}
		flow := buildFlowLog(msg, trace.SpanKindClient)
		handleError(code, err1, span, flow)
		if needToTraceBody(span, opt, err1) {
			flow.Request.Body = addEvent(ctx, req, semconv.RPCMessageTypeSent, sentDeadline, sentTime)
			flow.Response.Body = addEvent(ctx, rsp, semconv.RPCMessageTypeReceived, receivedDeadline, receivedTime)
		}
		span.SetAttributes(DefaultAttributesAfterClientHandle(ctx, rsp)...)
		span.SetAttributes(peerInfo(msg.RemoteAddr())...)
		span.SetAttributes(hostInfo(msg.LocalAddr())...)
		setHTTPClientAttrs(span, msg)
		flow.Cost = time.Since(start).String()

		return err
	}
}

func startClientSpan(ctx context.Context, req interface{}, msg codec.Msg) (context.Context, trace.Span) {
	var spanKind = trace.SpanKindClient
	if kind, ok := msg.CommonMeta()[SpanKindClient].(trace.SpanKind); ok {
		spanKind = kind
	}
	return getDefaultTracer().Start(ctx,
		// msg.ClientRPCName(),
		msg.CalleeServiceName()+"/"+strings.TrimLeft(msg.CalleeMethod(), "/"),
		trace.WithSpanKind(spanKind),
		trace.WithAttributes(CallerServiceKey.String(msg.CallerServiceName())),
		trace.WithAttributes(CallerMethodKey.String(msg.CallerMethod())),
		trace.WithAttributes(CalleeServiceKey.String(msg.CalleeServiceName())),
		trace.WithAttributes(CalleeMethodKey.String(msg.CalleeMethod())),
		trace.WithAttributes(NamespaceKey.String(msg.Namespace()),
			EnvNameKey.String(msg.EnvName())),
		trace.WithAttributes(DefaultTraceAttributesFunc(ctx, req)...),
		trace.WithAttributes(BaggageHeader.String(baggage.FromContext(ctx).String())))
}

func setHTTPClientAttrs(span trace.Span, msg codec.Msg) {
	if h, ok := msg.ClientReqHead().(*mhttp.ClientReqHeader); ok && h != nil && h.Request != nil {
		span.SetAttributes([]attribute.KeyValue{
			semconv.HTTPRequestMethodKey.String(h.Request.Method),
			HTTPRequestURLKey.String(h.Request.URL.String()),
		}...)
	}
	if h, ok := msg.ClientRspHead().(*mhttp.ClientRspHeader); ok && h != nil && h.Response != nil {
		span.SetAttributes([]attribute.KeyValue{
			semconv.HTTPResponseStatusCodeKey.Int(h.Response.StatusCode),
		}...)
	}
}

func toErrorType(t int) string {
	switch t {
	case errs.ErrorTypeBusiness:
		return "business"
	case errs.ErrorTypeCalleeFramework:
		return "callee_framework"
	case errs.ErrorTypeFramework:
		return "framework"
	default:
		return ""
	}
}

func getDeadline(ctx context.Context) time.Duration {
	var t time.Duration
	deadline, ok := ctx.Deadline()
	if ok {
		t = time.Until(deadline)
	}
	return t
}

// addEvent returns messageStr so that subsequent processing can be reused to reduce serialization consumption.
// The upper layer needs to judge whether it is empty. If it is empty,
// it means that the package body is not proto.Message and has not been serialized to string
func addEvent(ctx context.Context, message interface{},
	messageType attribute.KeyValue, deadline time.Duration, timeStamp time.Time) (messageStr string) {
	span := trace.SpanFromContext(ctx)
	defer func() {
		if err := recover(); err != nil {
			log.ErrorContextf(ctx, "opentelemetry addEvent err: %v", err)
			// add sdk panic metrics
			// panic is usually because DefaultTraceEventMarshalerWithContext puts related information
			// into the event for business analysis
			messageStr = fixStringTooLong(fmt.Sprintf("addEvent panic: %v", err))
			span.AddEvent(messageType.Value.AsString(),
				trace.WithAttributes(
					// RPCMessageUncompressedSizeKey is not accurate,
					// but it is much smaller than the proto.Size consumed by implicit serialization before pb1.4.0
					attribute.Key("message.detail").String(messageStr),
					attribute.Key("ctx.deadline").String(deadline.String()),
				),
				trace.WithTimestamp(timeStamp),
			)
		}
	}()

	messageStr = fixStringTooLong(DefaultTraceEventMarshalerWithContext(ctx, message))
	span.AddEvent(messageType.Value.AsString(),
		trace.WithAttributes(
			// RPCMessageUncompressedSizeKey is not accurate,
			// but it is much smaller than the proto.Size consumed by implicit serialization before pb1.4.0
			attribute.Key("message.detail").String(messageStr),
			attribute.Key("ctx.deadline").String(deadline.String()),
		),
		trace.WithTimestamp(timeStamp),
	)
	return messageStr
}

const fixedStringSuffix = "...stringLengthTooLong"

const defaultMaxStringLength = 32766

var maxStringLength = defaultMaxStringLength

// SetMaxStringLength sets the maximum length of a string attribute value.
func SetMaxStringLength(limit int) {
	if limit > defaultMaxStringLength {
		return
	}
	maxStringLength = limit
}

// isStringTooLong
func isStringTooLong(s string) bool {
	return len(s) > maxStringLength
}

// fixStringTooLong
// Document contains at least one immense term in field=\"logs.fields.value\"
// (whose UTF8 encoding is longer than the max length 32766)
func fixStringTooLong(s string) (result string) {
	if isStringTooLong(s) {
		return strings.ToValidUTF8(s[:maxStringLength-len(fixedStringSuffix)]+fixedStringSuffix, "")
	}
	return s
}
