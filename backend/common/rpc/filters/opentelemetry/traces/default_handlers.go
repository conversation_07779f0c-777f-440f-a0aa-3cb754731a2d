package traces

import (
	"context"

	"go.opentelemetry.io/otel/attribute"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
)

// TraceAttributesFunc hook for get trace attribute from ctx and req
type TraceAttributesFunc func(ctx context.Context, req interface{}) []attribute.KeyValue

// DefaultTraceAttributesFunc can be set by user
var DefaultTraceAttributesFunc TraceAttributesFunc = func(ctx context.Context, req interface{}) []attribute.KeyValue {
	return nil
}

// TraceEventMsgMarshalerWithContext marshaler for trace event msg with ctx
type TraceEventMsgMarshalerWithContext func(ctx context.Context, message interface{}) string

// DefaultTraceEventMarshalerWithContext can be set by user
var DefaultTraceEventMarshalerWithContext = func(_ context.Context, message interface{}) string {
	msg, _ := codec.Marshal(codec.SerializationTypeJSON, message)
	return string(msg)
}

// AttributesAfterHandle hook
type AttributesAfterHandle func(ctx context.Context, rsp interface{}) []attribute.KeyValue

// DefaultAttributesAfterServerHandle set by user
var DefaultAttributesAfterServerHandle AttributesAfterHandle = func(ctx context.Context,
	rsp interface{}) []attribute.KeyValue {
	return nil
}

// DefaultAttributesAfterClientHandle set by user
var DefaultAttributesAfterClientHandle AttributesAfterHandle = func(ctx context.Context,
	rsp interface{}) []attribute.KeyValue {
	return nil
}
