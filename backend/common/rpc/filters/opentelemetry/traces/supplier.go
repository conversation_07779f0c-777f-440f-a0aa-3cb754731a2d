package traces

import (
	"go.opentelemetry.io/otel/propagation"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	thttp "github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
)

func GetTextMapCarriers(md codec.MetaData, msg codec.Msg) propagation.TextMapCarrier {
	var textMapCarriers compositeTextMapCarrier
	textMapCarriers = append(textMapCarriers, newSupplier(md, msg))
	return &textMapCarriers
}

func newSupplier(md codec.MetaData, msg codec.Msg) *supplier {
	serverHeader := thttp.Head(msg.Context())
	clientHeader, _ := msg.ClientReqHead().(*thttp.ClientReqHeader)
	return &supplier{
		md:           md,
		serverHeader: serverHeader,
		clientHeader: clientHeader,
		keys:         make([]string, 0),
	}
}

type supplier struct {
	md           codec.MetaData
	serverHeader *thttp.Header
	clientHeader *thttp.ClientReqHeader
	keys         []string
}

func (s *supplier) Get(key string) string {
	values := s.md[key]
	if len(values) == 0 {
		if s.serverHeader != nil && s.serverHeader.Request != nil && s.serverHeader.Request.Header != nil {
			return s.serverHeader.Request.Header.Get(key)
		}
		return ""
	}
	return values[0]
}

func (s *supplier) Set(key string, value string) {
	s.md[key] = []string{value}
	if s.clientHeader != nil {
		s.clientHeader.AddHeader(key, value)
	}
	s.keys = append(s.keys, key)
}

func (s *supplier) Keys() []string {
	return s.keys
}

type compositeTextMapCarrier []propagation.TextMapCarrier

func (c *compositeTextMapCarrier) Get(key string) string {
	v := ""
	for _, carrier := range *c {
		v = carrier.Get(key)
		if v != "" {
			break
		}
	}
	return v
}

func (c *compositeTextMapCarrier) Set(key string, value string) {
	for _, carrier := range *c {
		carrier.Set(key, value)
	}
}

func (c *compositeTextMapCarrier) Keys() []string {
	keys := make(map[string]bool)
	var list []string

	for _, carrier := range *c {
		for _, k := range carrier.Keys() {
			if _, value := keys[k]; !value {
				keys[k] = true
				list = append(list, k)
			}
		}
	}
	return list
}
