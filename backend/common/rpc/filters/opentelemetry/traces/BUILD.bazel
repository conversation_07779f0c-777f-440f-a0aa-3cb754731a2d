load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "traces",
    srcs = [
        "codes.go",
        "default_handlers.go",
        "host.go",
        "semconv.go",
        "supplier.go",
        "traces.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry/traces",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/filters/opentelemetry/logs",
        "//backend/common/rpc/framework/codec",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/filter",
        "//backend/common/rpc/framework/http",
        "//backend/common/rpc/framework/log",
        "@com_github_datadog_dd_trace_go_v2//ddtrace/opentelemetry",
        "@com_github_datadog_dd_trace_go_v2//ddtrace/tracer",
        "@io_opentelemetry_go_otel//:otel",
        "@io_opentelemetry_go_otel//attribute",
        "@io_opentelemetry_go_otel//baggage",
        "@io_opentelemetry_go_otel//codes",
        "@io_opentelemetry_go_otel//propagation",
        "@io_opentelemetry_go_otel//semconv/v1.31.0:v1_31_0",
        "@io_opentelemetry_go_otel//semconv/v1.32.0:v1_32_0",
        "@io_opentelemetry_go_otel_trace//:trace",
    ],
)
