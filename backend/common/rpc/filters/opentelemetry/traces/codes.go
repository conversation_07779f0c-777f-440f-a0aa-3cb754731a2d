package traces

import (
	"context"
	"strconv"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
)

// GetStringCodeFunc defaultGetCodeFunc
type GetStringCodeFunc func(ctx context.Context, rsp interface{}, err error) (string, error)

// defaultGetCodeFunc defaultGetCodeFunc
// user can set a custom GetStringCodeFunc
var defaultGetCodeFunc GetStringCodeFunc = func(ctx context.Context, rsp interface{}, err error) (string, error) {
	if err != nil {
		return strconv.Itoa(int(errs.Code(err))), err
	}
	switch v := rsp.(type) {
	case interface {
		GetRetcode() int32
	}:
		return strconv.Itoa(int(v.GetRetcode())), err
	case interface {
		GetRetCode() int32
	}:
		return strconv.Itoa(int(v.GetRetCode())), err
	case interface {
		GetCode() int32
	}:
		return strconv.Itoa(int(v.GetCode())), err
	default:
		return "0", err
	}
}

// SetDefaultGetCodeFunc user can set a custom GetStringCodeFunc
func SetDefaultGetCodeFunc(f GetStringCodeFunc) {
	defaultGetCodeFunc = f
}

// GetDefaultGetCodeFunc getDefaultGetCodeFunc
func GetDefaultGetCodeFunc() GetStringCodeFunc {
	return defaultGetCodeFunc
}
