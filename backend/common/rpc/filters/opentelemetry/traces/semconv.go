package traces

import "go.opentelemetry.io/otel/attribute"

const (
	NamespaceKey = attribute.Key("rpc.namespace")
	EnvNameKey   = attribute.Key("rpc.envname")

	StatusCode = attribute.Key("rpc.status_code")
	StatusMsg  = attribute.Key("rpc.status_msg")
	StatusType = attribute.Key("rpc.status_type")

	ProtocolKey = attribute.Key("rpc.protocol")

	CallerServiceKey = attribute.Key("rpc.caller_service")
	CallerMethodKey  = attribute.Key("rpc.caller_method")
	CalleeServiceKey = attribute.Key("rpc.callee_service")
	CalleeMethodKey  = attribute.Key("rpc.callee_method")

	BaggageHeader = attribute.Key("baggage")

	HTTPRequestURLKey = attribute.Key("http.request.url")
)
