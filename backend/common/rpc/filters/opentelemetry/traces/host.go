package traces

import (
	"net"
	"os"
	"sync"

	"go.opentelemetry.io/otel/attribute"
	semconv "go.opentelemetry.io/otel/semconv/v1.31.0"
)

var (
	hostname     string
	hostnameOnce sync.Once
)

const localhost = "127.0.0.1"

func peerInfo(addr net.Addr) []attribute.KeyValue {
	if addr == nil {
		return nil
	}
	host, port, err := net.SplitHostPort(addr.String())

	if err != nil {
		return []attribute.KeyValue{}
	}

	if host == "" {
		host = localhost
	}

	return []attribute.KeyValue{
		semconv.NetworkPeerAddressKey.String(host),
		semconv.NetworkPeerPortKey.String(port),
	}
}

func hostInfo(addr net.Addr) []attribute.KeyValue {
	if addr == nil {
		return []attribute.KeyValue{
			semconv.HostNameKey.String(getHostname()),
		}
	}
	host, port, err := net.SplitHostPort(addr.String())

	if err != nil {
		return []attribute.KeyValue{
			semconv.HostNameKey.String(getHostname()),
		}
	}

	if host == "" {
		host = localhost
	}

	return []attribute.KeyValue{
		semconv.NetworkLocalAddressKey.String(host),
		semconv.NetworkLocalPortKey.String(port),
		semconv.HostNameKey.String(getHostname()),
	}
}

// getHostname
func getHostname() string {
	hostnameOnce.Do(func() {
		hostname, _ = os.Hostname()
	})
	return hostname
}
