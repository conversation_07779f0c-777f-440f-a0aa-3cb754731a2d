package otel

import (
	"github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry/logs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry/traces"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/filter"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/plugin"
)

const (
	PluginType = "telemetry"
	PluginName = "opentelemetry"
)

var (
	ServerFilter = filter.ServerChain{traces.ServerFilter()}.Filter
	ClientFilter = filter.ClientChain{traces.ClientFilter()}.Filter
)

func init() {
	plugin.Register(PluginName, &Plugin{})
	filter.Register(PluginName, ServerFilter, ClientFilter)
}

type Plugin struct{}

func (f *Plugin) Type() string {
	return PluginType
}

func (f *Plugin) Setup(_ string, configDec plugin.Decoder) error {
	c := &Config{}
	if err := configDec.Decode(c); err != nil {
		return err
	}
	if err := traces.Setup(); err != nil {
		return err
	}
	setupFilters(c)
	return nil
}

func setupFilters(cfg *Config) {
	filterOpts := func(o *traces.FilterOptions) {
		o.DisableTraceBody = cfg.Traces.DisableTraceBody
	}
	serverFilterChain := filter.ServerChain{traces.ServerFilter(filterOpts), logs.ServerFilter()}
	clientFilterChain := filter.ClientChain{traces.ClientFilter(filterOpts), logs.ClientFilter()}
	serverFilter := serverFilterChain.Filter
	clientFilter := clientFilterChain.Filter
	filter.Register(PluginName, serverFilter, clientFilter)
}

type Config struct {
	Traces TracesConfig `yaml:"traces"`
}

type TracesConfig struct {
	DisableTraceBody bool `yaml:"disable_trace_body"`
}
