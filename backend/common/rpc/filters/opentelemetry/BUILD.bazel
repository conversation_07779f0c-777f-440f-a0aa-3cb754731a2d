load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "opentelemetry",
    srcs = ["opentelemetry.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/filters/opentelemetry/logs",
        "//backend/common/rpc/filters/opentelemetry/traces",
        "//backend/common/rpc/framework/filter",
        "//backend/common/rpc/framework/plugin",
    ],
)
