package growthbook

import (
	"context"
	"time"

	"github.com/bytedance/sonic"
	growthbook "github.com/growthbook/growthbook-golang"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/plugin"
)

const (
	pluginType = "config"
	pluginName = "growth_book"
)

var (
	GrowthBookClient = &Plugin{}
)

func init() {
	plugin.Register(pluginName, GrowthBookClient)
}

type Config struct {
	Host      string        `yaml:"host"`
	ClientKey string        `yaml:"client_key"`
	Timeout   time.Duration `yaml:"timeout"`
}

type Plugin struct {
	Client *growthbook.Client
}

func (k *Plugin) Type() string {
	return pluginType
}

func (k *Plugin) Setup(name string, configDesc plugin.Decoder) error {
	var config Config
	if err := configDesc.Decode(&config); err != nil {
		return err
	}
	configStr, _ := sonic.MarshalString(config)
	log.Infof("growth_book Plugin config:%s", configStr)

	// new client
	cli, err := growthbook.NewClient(context.Background(),
		growthbook.WithApiHost(config.Host),
		growthbook.WithClientKey(config.ClientKey),
		growthbook.WithPollDataSource(config.Timeout),
	)
	if err != nil {
		panic("growth book init fail")
	}
	if err := cli.EnsureLoaded(context.Background()); err != nil {
		panic("growth book load fail")
	}
	k.Client = cli
	return nil
}
