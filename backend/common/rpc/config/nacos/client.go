package nacos

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/nacos-group/nacos-sdk-go/v2/clients"
	"github.com/nacos-group/nacos-sdk-go/v2/clients/config_client"
	"github.com/nacos-group/nacos-sdk-go/v2/common/constant"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
	"go.uber.org/zap"

	"github.com/MoeGolibrary/go-lib/zlog"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
)

// Config nacos config
type Config struct {
	// Name is the name of the Plugin
	Name string `yaml:"name"`
	// Enabled enables remote config
	Enabled bool `yaml:"enabled"`
	// ServerAddr is the address of the remote config server
	ServerAddr string `yaml:"server_addr"`
	// Namespace is the namespace of the remote config
	Namespace string `yaml:"namespace"`
	// Username is the username of the remote config
	Username string `yaml:"username"`
	// Password is the password of the remote config
	Password string `yaml:"password"`
	// appName is the app name of the remote configs
	AppName string `yaml:"app_name"`
	// TimeoutMs is the timeout of the remote config
	TimeoutMs uint64 `yaml:"timeout_ms"`
	// NotLoadCacheAtStart enables not load cache at start
	NotLoadCacheAtStart bool `yaml:"not_load_cache_at_start"`
	// LogDir is the log dir of the remote config
	LogLevel string `yaml:"log_level"`
	// LogDir is the log dir of the remote config
	LogDir string `yaml:"log_dir"`
	// CacheDir is the cache dir of the remote config
	CacheDir string `yaml:"cache_dir"`
}

// CheckAndApplyDefaultConfig use default config when param is empty
func (c *Config) CheckAndApplyDefaultConfig() error {
	if c.Name == "" {
		c.Name = "nacos"
	}
	if c.TimeoutMs == 0 {
		c.TimeoutMs = 1000
	}
	if c.LogLevel == "" {
		c.LogLevel = "warn"
	}
	if c.CacheDir == "" {
		c.CacheDir = "./nacos/cache/config"
	}
	if c.LogDir == "" {
		c.LogDir = "./nacos/log/config"
	}
	if c.AppName == "" {
		c.AppName = framework.GlobalConfig().Server.App
	}
	return nil
}

// Client nacos client wrapper
type Client struct {
	cfg    *Config
	client config_client.IConfigClient
}

// New create a nacos client
func New(c *Config) (*Client, error) {
	if !c.Enabled {
		return nil, errors.New("nacos config is disabled")
	}
	//create ServerConfig
	sc := buildServerConfigs(c.ServerAddr)

	cc := *constant.NewClientConfig(
		constant.WithNamespaceId(c.Namespace),
		constant.WithUsername(c.Username),
		constant.WithPassword(c.Password),
		constant.WithTimeoutMs(c.TimeoutMs),
		constant.WithNotLoadCacheAtStart(c.NotLoadCacheAtStart),
		constant.WithLogDir(c.LogDir),
		constant.WithCacheDir(c.CacheDir),
		constant.WithLogLevel(c.LogLevel),
		constant.WithAppName(c.AppName),
		constant.WithUpdateCacheWhenEmpty(true),
	)

	client, err := clients.NewConfigClient(
		vo.NacosClientParam{
			ClientConfig:  &cc,
			ServerConfigs: sc,
		})
	if err != nil {
		return nil, err
	}
	return &Client{
		cfg:    c,
		client: client,
	}, nil
}

// GrpcPortOffset 是 gRPC 端口的偏移量
const (
	GrpcPortOffset = 1000
	addrPartsLen   = 2
)

// buildServerConfigs 构建 ServerConfig 列表
func buildServerConfigs(serverAddrs string) []constant.ServerConfig {
	var serverConfigs []constant.ServerConfig

	// 分割服务器地址
	parts := strings.Split(serverAddrs, ",")
	for _, serverAddr := range parts {
		if serverAddr == "" {
			continue
		}

		// 分割 IP 地址和端口
		addrParts := strings.Split(serverAddr, ":")
		if len(addrParts) != addrPartsLen {
			// 日志记录无效服务器地址格式的信息
			zlog.Warn(context.Background(), "invalid server address format", zap.String("address", serverAddr))
			continue
		}

		// 解析 IP 地址和端口
		ipAddr := addrParts[0]
		port, err := strconv.ParseUint(addrParts[1], 10, 64)
		if err != nil {
			// 日志记录解析端口失败的信息
			zlog.Warn(context.Background(), "failed to parse port", zap.String("address", serverAddr), zap.Error(err))
			continue
		}

		// 计算 gRPC 端口
		grpcPort := port + GrpcPortOffset
		// 构建并添加 ServerConfig
		serverConfigs = append(serverConfigs, constant.ServerConfig{
			ContextPath: "/nacos",
			IpAddr:      ipAddr,
			Port:        port,
			GrpcPort:    grpcPort,
		})
	}

	return serverConfigs
}

// Name get name of provider
func (c *Client) Name() string {
	return c.cfg.Name
}

func (c *Client) GetGroup() string {
	return framework.GlobalConfig().Server.Server
}

func (c *Client) GetAppName() string {
	return c.cfg.AppName
}

// Get get config by nacos key
func (c *Client) Get(ctx context.Context, key string, opts ...config.Option) (config.Response, error) {
	content, err := c.client.GetConfig(vo.ConfigParam{
		DataId: key,
		Group:  c.GetGroup(),
	})
	if err != nil {
		return nil, err
	}
	rsp := &nacosResponse{event: config.EventTypeNull, value: content, meta: make(map[string]string)}
	return rsp, nil
}

// Put update key
func (c *Client) Put(ctx context.Context, key, val string, opts ...config.Option) error {
	success, err := c.client.PublishConfig(vo.ConfigParam{
		DataId:  key,
		Group:   c.GetGroup(),
		Content: val,
	})
	if err != nil || !success {
		return fmt.Errorf("nacos put config for key %s err: %w", key, err)
	}
	return nil
}

// Del delete key
func (c *Client) Del(ctx context.Context, key string, opts ...config.Option) error {
	success, err := c.client.DeleteConfig(vo.ConfigParam{
		DataId: key,
		Group:  c.GetGroup(),
	})
	if err != nil || !success {
		return fmt.Errorf("nacos delete config for key %s err: %w", key, err)
	}
	return nil
}

// Watch monitor key changing
func (c *Client) Watch(ctx context.Context, key string, opts ...config.Option) (<-chan config.Response, error) {
	rspChan := make(chan config.Response, 1)
	err := c.client.ListenConfig(vo.ConfigParam{
		DataId: key,
		Group:  c.GetGroup(),
		OnChange: func(namespace, group, dataId, data string) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Error(context.Background(), "panic in nacos config watch callback", zap.Any("recover", r))
				}
			}()
			rspChan <- &nacosResponse{
				event: config.EventTypePut,
				value: data,
				meta:  make(map[string]string),
			}
		},
	})
	return rspChan, err
}

type nacosResponse struct {
	event config.EventType
	value string
	meta  map[string]string
}

// Value config content
func (r *nacosResponse) Value() string {
	return r.value
}

// Event config event
func (r *nacosResponse) Event() config.EventType {
	return r.event
}

// MetaData config meda data
func (r *nacosResponse) MetaData() map[string]string {
	return r.meta
}
