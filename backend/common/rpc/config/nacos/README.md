# Nacos 配置插件

本插件提供基于 [Nacos](https://nacos.io/) 的配置中心能力，支持远程配置的获取、更新、删除及监听操作，适用于 tRPC 框架的配置管理场景。

---

## 1. 配置

在配置文件中添加如下插件配置：

```yaml
# 配置中心的Secret Manager
secrets:
  - name: "moego/testing/nacos"
    prefix: "secret.nacos."

server:
  app: fanbook # 业务的应用名，对应 Nacos 配置的 "归属应用"
  server: member_list_sort # 进程服务名，对应 Nacos 配置的 "Group"

plugins: # 插件配置
  config:
    nacos:
      providers:
        - name: nacos
          enabled: true # 是否启用远程配置，默认 false
          server_addr: ${secret.nacos.server-addr} # 配置中心地址
          username: ${secret.nacos.username} # 用户名
          password: ${secret.nacos.password} # 密码
          namespace: ${secret.nacos.namespace} # 命名空间
          timeout_ms: 10000 # 调用 Nacos 接口超时时间
          not_load_cache_at_start: true # 启动时是否从本地缓存加载配置
          cache_dir: /data/trpc-go/member_list_sort # 缓存路径
          log_level: debug # 日志等级
          log_dir: /data/trpc-go/member_list_sort # 日志存放路径
```

---

## 2. 使用指南

### 2.1. 注册插件

在程序入口 main.go 中导入插件包以完成注册：

```go
import (
    _ "github.com/MoeGolibrary/moego/backend/common/rpc/config/nacos"
)
```

---

### 2.2. 获取配置

```go
package main

import (
	"context"
	"fmt"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	rconfig "github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
	"gopkg.in/yaml.v3"
)

func main() {
	cfgClient := rconfig.Get("nacos")

	// 获取配置
	r, err := cfgClient.Get(context.Background(), "sort_config.yaml")
	if err != nil {
		log.Fatalf("Failed to get config: %v", err)
	}
	fmt.Printf("Current config: %s\n", r.Value())

	// 反序列化为结构体
	type SortConfig struct {
		Strategy string `yaml:"strategy"`
		Timeout  int    `yaml:"timeout"`
	}
	var configData SortConfig
	if err := yaml.Unmarshal([]byte(r.Value()), &configData); err != nil {
		log.Fatalf("Failed to unmarshal config: %v", err)
	}
	fmt.Printf("Parsed config: %+v\n", configData)
}
```

---

### 2.3. 更新配置

```go
err := cfgClient.Put(context.Background(), "sort_config.yaml", "strategy: descending\ntimeout: 30")
if err != nil {
    log.Fatalf("Failed to update config: %v", err)
}
```

---

### 2.4. 删除配置

```go
err := cfgClient.Del(context.Background(), "sort_config.yaml")
if err != nil {
    log.Fatalf("Failed to delete config: %v", err)
}
```

---

### 2.5. 监听配置变更

```go
c, err := cfgClient.Watch(context.Background(), "sort_config.yaml")
if err != nil {
    log.Fatalf("Failed to watch config: %v", err)
}

// 启动协程监听变更
go func () {
    for update := range c {
        log.Printf("Config updated: %s", update.Value())
        // 可在此处重新加载配置并更新运行时状态
    }
}()
```

---

### 2.6. 完整示例代码

```go
package main

import (
	"context"
	"fmt"
	"time"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	rconfig "github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
	"gopkg.in/yaml.v3"
)

type SortConfig struct {
	Strategy string `yaml:"strategy"`
	Timeout  int    `yaml:"timeout"`
}

func main() {
	cfgClient := rconfig.Get("nacos")

	// 1. 获取配置
	resp, err := cfgClient.Get(context.Background(), "sort_config.yaml")
	if err != nil {
		log.Fatalf("Failed to get config: %v", err)
	}

	var configData SortConfig
	if err := yaml.Unmarshal([]byte(resp.Value()), &configData); err != nil {
		log.Fatalf("Failed to unmarshal config: %v", err)
	}
	fmt.Printf("Initial config: %+v\n", configData)

	// 2. 更新配置
	newConfig := "strategy: descending\ntimeout: 30"
	err = cfgClient.Put(context.Background(), "sort_config.yaml", newConfig)
	if err != nil {
		log.Fatalf("Failed to update config: %v", err)
	}

	// 3. 删除配置
	err = cfgClient.Del(context.Background(), "sort_config.yaml")
	if err != nil {
		log.Fatalf("Failed to delete config: %v", err)
	}

	// 4. 监听配置变更（可选）
	c, err := cfgClient.Watch(context.Background(), "sort_config.yaml")
	if err != nil {
		log.Fatalf("Failed to watch config: %v", err)
	}

	go func() {
		for update := range c {
			log.Printf("Config updated: %s", update.Value())
			// 可在此处处理运行时配置更新逻辑
		}
	}()

	// 防止主函数退出
	time.Sleep(5 * time.Second)
}
```

---

## 3. 注意事项

- **线程安全**：在 Watch 回调中更新共享资源时，应使用锁机制确保线程安全。
- **性能影响**：频繁调用 Put 和 Del 可能会影响 Nacos 服务性能，建议合理控制调用频率。
- **权限配置**：确保 Nacos 用户具备 PublishConfig 和 DeleteConfig 权限。
- **配置格式**：支持 YAML、JSON、TOML 等格式，需确保配置内容与反序列化结构一致。

---

如需进一步定制 Nacos 客户端行为，可参考 [client.go](./client.go) 中的 Config 结构和 buildServerConfigs 方法进行扩展。

