load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "redis",
    srcs = ["plugin.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/config/redis",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/common/rpc/framework/plugin",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_redis_go_redis_v9//:go-redis",
    ],
)
