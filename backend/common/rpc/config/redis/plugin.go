package redis

import (
	"context"
	"crypto/tls"
	"fmt"
	"strconv"

	"github.com/bytedance/sonic"
	redis "github.com/redis/go-redis/v9"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/plugin"
)

const (
	pluginType = "config"
	pluginName = "redis"
)

var (
	RedisClient = &Plugin{}
)

func init() {
	plugin.Register(pluginName, RedisClient)
}

type Config struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	Password string `yaml:"password"`
	TLS      string `yaml:"tls"`
}

type Plugin struct {
	Client *redis.Client
}

func (k *Plugin) Type() string {
	return pluginType
}

func (k *Plugin) Setup(name string, configDesc plugin.Decoder) error {
	var config Config
	if err := configDesc.Decode(&config); err != nil {
		return err
	}
	configStr, _ := sonic.MarshalString(config)
	log.Infof("redis Plugin config:%s", configStr)

	// new client
	opts := &redis.Options{
		Addr:     fmt.Sprintf("%s:%s", config.Host, config.Port),
		Password: config.Password,
	}
	if v, err := strconv.ParseBool(config.TLS); err == nil && v {
		opts.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	}
	cli := redis.NewClient(opts)
	if _, err := cli.Ping(context.Background()).Result(); err != nil {
		panic(err)
	}
	k.Client = cli
	log.Infof("redis Plugin init success")
	return nil
}
