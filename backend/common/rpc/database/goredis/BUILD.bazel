load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "goredis",
    srcs = ["client.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/database/goredis",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/database/goredis/filters",
        "//backend/common/rpc/database/goredis/options",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/client",
        "//backend/common/rpc/framework/codec",
        "//backend/common/rpc/framework/errs",
        "@com_github_redis_go_redis_v9//:go-redis",
    ],
)
