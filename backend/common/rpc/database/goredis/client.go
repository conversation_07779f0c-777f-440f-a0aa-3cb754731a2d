// Package goredis extend github.com/go-redis/redis to add support for the framework-go ecosystem.
package goredis

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"net"
	"strings"
	"unicode/utf8"

	redis "github.com/redis/go-redis/v9"

	"github.com/MoeGolibrary/moego/backend/common/rpc/database/goredis/filters"
	"github.com/MoeGolibrary/moego/backend/common/rpc/database/goredis/options"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/client"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
)

// ContextKeyType go redis context msg key type
type ContextKeyType string

const (
	ContextKeyName ContextKeyType = "rpc-go-redis" // context key

	RetRedisNil     = 30001 // redis field doesn't exist
	RetCASMismatch  = 30002 // cas confict
	RetRedisCmdFail = 30003 // redis cmd fail
	RetParamInvalid = 30004 // invalid parameter
	RetTypeMismatch = 30005 // type mismatch
	RetLockOccupied = 30006 // lock is already occupied
	RetLockExpired  = 30007 // lock has expired
	RetLockRobbed   = 30008 // lock has been seized
	RetKeyNotFound  = 30009 // key doesn't exist or expired, it will alert but RetRedisNil will not
	RetAddCronFail  = 30010 // fail to add the cron job
	RetInitFail     = 30011 // fail to initiate
	RetLockExtend   = 30012 // fail to renew the lock

	InfinityMin = "-inf" // negative infinity
	InfinityMax = "+inf" // positive infinity

	calleeApp = "[redis]"              // redis callee app.
	appKey    = "overrideCalleeApp"    // metadata app key.
	serverKey = "overrideCalleeServer" // metadata server key.
)

var (
	MaxRspLen int64 = 100 // max response length.
)

// New creates a redis client.
var New = func(name string, opts ...client.Option) (
	redis.UniversalClient, error) {
	// parse configuration file.
	filters, err := filters.New(name, opts...)
	if err != nil {
		return nil, errs.Wrapf(err, RetInitFail, "filters.NewFilters fail %v", err)
	}
	rpcOptions, err := filters.LoadClientOptions()
	if err != nil {
		return nil, errs.Wrapf(err, RetInitFail, "filters.GetClientOptions fail %v", err)
	}
	option, err := options.New(rpcOptions)
	if err != nil {
		return nil, err
	}
	redisClient := redis.NewUniversalClient(option.RedisOption)
	redisHook, err := newHook(filters, option)
	if err != nil {
		return nil, err
	}
	redisClient.AddHook(redisHook)
	// check the connection health
	if _, err = redisClient.Ping(framework.BackgroundContext()).Result(); err != nil {
		return nil, errs.Wrapf(err, RetInitFail, "New Ping fail %v", err)
	}
	return redisClient, nil
}

// Req framework filter request.
type Req struct {
	Cmd string
}

// Rsp framework filter response.
type Rsp struct {
	Cmd string
}

// Message redis context
type Message struct {
	EnableFilter bool            // enable filters or not
	Options      []client.Option // framework client options for a single request
}

// WithMessage gets Message.
func WithMessage(ctx context.Context) (context.Context, *Message) {
	vi := ctx.Value(ContextKeyName)
	vm, _ := vi.(*Message)
	if vm != nil {
		return ctx, vm
	}
	msg := &Message{
		EnableFilter: true,
	}
	return context.WithValue(ctx, ContextKeyName, msg), msg
}

// hook redis hook.
type hook struct {
	filters    *filters.Filters
	remoteAddr net.Addr
	options    *options.Options
}

func newHook(f *filters.Filters, o *options.Options) (*hook, error) {
	h := &hook{
		filters: f,
		options: o,
	}
	var err error
	addr := h.options.RedisOption.Addrs[0]
	if h.remoteAddr, err = net.ResolveTCPAddr("tcp", addr); err != nil {
		return nil, errs.Wrapf(err, RetInitFail, "net.ResolveTCPAddr fail %s %v", addr, err)
	}
	return h, nil
}

// DialHook is triggered during the dial process.
func (h *hook) DialHook(next redis.DialHook) redis.DialHook {
	return func(ctx context.Context, network, addr string) (net.Conn, error) {
		return next(ctx, network, addr)
	}
}

// ProcessHook is triggered when sending single redis command.
func (h *hook) ProcessHook(next redis.ProcessHook) redis.ProcessHook {
	return func(ctx context.Context, cmd redis.Cmder) error {
		// redis parameter
		ctx, redisMsg := WithMessage(ctx)
		if !redisMsg.EnableFilter {
			return next(ctx, cmd)
		}
		reqBody := nameKey(cmd)
		call := func(context.Context) (string, error) {
			nextErr := next(ctx, cmd)
			rspBody := fixedResponseLength(reqBody, cmd.String(), MaxRspLen)
			return rspBody, nextErr
		}
		req := &invokeReq{
			rpcName:      cmd.Name(),
			calleeMethod: "/redis/" + cmd.Name(),
			reqBody:      reqBody,
			call:         call,
		}
		return h.invoke(ctx, req, redisMsg.Options...)
	}
}

// ProcessPipelineHook is triggered when sending pipeline redis command.
func (h *hook) ProcessPipelineHook(next redis.ProcessPipelineHook) redis.ProcessPipelineHook {
	return func(ctx context.Context, cmds []redis.Cmder) error {
		ctx, redisMsg := WithMessage(ctx)
		if !redisMsg.EnableFilter {
			return next(ctx, cmds)
		}
		call := func(context.Context) (string, error) {
			nextErr := next(ctx, cmds)
			rspBody := pipelineRspBody(cmds)
			return rspBody, nextErr
		}
		req := &invokeReq{
			rpcName: "pipeline",
			call:    call,
		}
		req.calleeMethod, req.reqBody = pipelineReqBody(cmds)
		return h.invoke(ctx, req, redisMsg.Options...)
	}
}

type invokeReq struct {
	rpcName      string
	calleeMethod string
	reqBody      string
	call         func(context.Context) (string, error)
}

func (h *hook) invoke(ctx context.Context, req *invokeReq, opts ...client.Option) error {
	ctx = h.fillTRPCMessage(ctx, req.rpcName, req.calleeMethod)
	var (
		callErr error
		rReq    = &Req{Cmd: req.reqBody}
		rRsp    = &Rsp{}
	)
	rpcErr := h.filters.Invoke(ctx, rReq, rRsp, func(ctx context.Context, _, _ interface{}) error {
		rRsp.Cmd, callErr = req.call(ctx)
		return TRPCErr(callErr)
	}, opts...)
	if callErr != nil {
		return callErr
	}
	return rpcErr
}

func fixedResponseLength(reqCmd, rspCmd string, max int64) string {
	rsp := rspCmd
	if strings.Index(rsp, reqCmd) == 0 {
		rsp = rsp[len(reqCmd):]
	}
	if int64(len(rsp)) > max {
		rsp = rsp[:max]
	}
	if !utf8.ValidString(rsp) {
		rsp = base64.StdEncoding.EncodeToString([]byte(rsp))
	}
	if int64(len(rsp)) > max {
		rsp = rsp[:max]
	}
	return rsp
}

// nameKey simpify request.
func nameKey(cmd redis.Cmder) string {
	args := cmd.Args()
	if len(args) == 1 {
		return cmd.Name()
	}
	return fmt.Sprintf(`%s %s`, args[0], args[1])
}

// fillTRPCMessage fills framework message.
func (h *hook) fillTRPCMessage(ctx context.Context, rpcName, calleeMethod string) context.Context {
	var rpcMsg codec.Msg
	ctx, rpcMsg = codec.WithCloneMessage(ctx)
	rpcName = strings.ReplaceAll(rpcName, "/", "_")
	rpcMsg.WithClientRPCName(fmt.Sprintf("/%s/%s", h.filters.Name, rpcName))
	rpcMsg.WithCalleeMethod(calleeMethod)
	rpcMsg.WithCalleeServiceName(h.filters.Name)
	withCommonMetaData(rpcMsg, calleeApp, h.remoteAddr.String())
	rpcMsg.WithRemoteAddr(h.remoteAddr)
	return ctx
}

// withCommonMetaData fills redis endpoint in meta data.
func withCommonMetaData(msg codec.Msg, calleeApp, calleeServer string) {
	meta := msg.CommonMeta()
	if meta == nil {
		meta = codec.CommonMeta{}
	}
	meta[appKey] = calleeApp
	meta[serverKey] = calleeServer
	msg.WithCommonMeta(meta)
}

// pipelineReqBody pipeline request.
func pipelineReqBody(commands []redis.Cmder) (string, string) {
	reqBody := bytes.NewBufferString("[")
	calleeMethod := bytes.NewBufferString("/redis/pipeline")
	cmdMap := make(map[string]bool)
	for i, cmd := range commands {
		if i != 0 {
			reqBody.WriteString(",")
		}
		reqBody.WriteString(nameKey(cmd))
		if _, ok := cmdMap[cmd.Name()]; !ok {
			cmdMap[cmd.Name()] = true
			calleeMethod.WriteString("/" + cmd.Name())
		}
	}
	reqBody.WriteString("]")
	return calleeMethod.String(), reqBody.String()
}

// pipelineRspBody pipeline response.
func pipelineRspBody(commands []redis.Cmder) string {
	rspBody := bytes.NewBufferString("[")
	for i, cmd := range commands {
		if i != 0 {
			rspBody.WriteString(",")
		}
		if cmd.Err() != nil {
			rspBody.WriteString(cmd.Err().Error())
		} else {
			rspBody.WriteString("nil")
		}
	}
	rspBody.WriteString("]")
	return rspBody.String()
}

// TRPCErr convert error to framework format.
func TRPCErr(err error) error {
	switch err {
	case nil, redis.Nil:
		return nil
	default:
		if _, ok := err.(*errs.Error); ok {
			return err
		}
		msg := err.Error()
		return errs.Newm(RetRedisCmdFail, msg)
	}
}
