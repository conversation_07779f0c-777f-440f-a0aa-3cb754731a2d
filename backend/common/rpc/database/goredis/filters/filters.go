package filters

import (
	"context"
	"fmt"
	"time"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/client"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/filter"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport"
)

// Filters is filter adapter.
type Filters struct {
	Name string
	Opts []client.Option
}

// New creates a collection of filters.
func New(name string, opts ...client.Option) (*Filters, error) {
	f := &Filters{
		Name: name,
		Opts: opts,
	}
	return f, nil
}

// Invoke callbacks mode request.
func (f *Filters) Invoke(ctx context.Context, req, rsp interface{}, call filter.ClientHandleFunc,
	opts ...client.Option) error {
	// Get filter.
	options, err := f.LoadClientOptions(opts...)
	if err != nil {
		return err
	}
	// Set timeout.
	if options.Timeout > 0 {
		var cancel context.CancelFunc // Calculation timed out.
		ctx, cancel = context.WithTimeout(ctx, options.Timeout)
		defer cancel()
	}
	// Execute filter.
	return options.Filters.Filter(ctx, req, rsp, call)
}

// LoadClientOptions parses the caller service configuration.
func (f *Filters) LoadClientOptions(opts ...client.Option) (*client.Options, error) {
	// Parsing configuration parameters
	clientOptions := &client.Options{
		Transport:                transport.DefaultClientTransport,
		SerializationType:        -1, // Initial value -1, do not set
		CurrentSerializationType: -1, // The serialization method of the current client.
		// The serialization method in the protocol is based on the SerializationType,
		// and the forwarding proxy situation.
		CurrentCompressType: -1, // The current client transparent transmission body is not serialized,
		// but the backend of the business agreement needs to specify the serialization method
	}
	// Use the servicename (package.service) of the protocol file
	// of the transferred party as the key to obtain the relevant configuration.
	err := loadClientConfig(clientOptions, f.Name)
	if err != nil {
		return nil, err
	}
	// The input parameter is the highest priority to overwrite the original data.
	for _, o := range f.Opts {
		o(clientOptions)
	}
	for _, o := range opts {
		o(clientOptions)
	}
	err = loadClientFilterConfig(clientOptions, f.Name)
	if err != nil {
		return nil, err
	}
	return clientOptions, nil
}

func loadClientConfig(opts *client.Options, key string) error {
	cfg, ok := client.DefaultClientConfig()[key]
	if !ok {
		return nil
	}
	if err := setNamingOptions(opts, cfg); err != nil {
		return err
	}

	if cfg.Timeout > 0 {
		opts.Timeout = time.Duration(cfg.Timeout) * time.Millisecond
	}
	if cfg.Serialization != nil {
		opts.SerializationType = *cfg.Serialization
	}

	if cfg.Compression > codec.CompressTypeNoop {
		opts.CompressType = cfg.Compression
	}
	if cfg.Protocol != "" {
		o := client.WithProtocol(cfg.Protocol)
		o(opts)
	}
	if cfg.Network != "" {
		opts.Network = cfg.Network
		opts.CallOptions = append(opts.CallOptions, transport.WithDialNetwork(cfg.Network))
	}
	if cfg.Password != "" {
		opts.CallOptions = append(opts.CallOptions, transport.WithDialPassword(cfg.Password))
	}
	return nil
}

func setNamingOptions(opts *client.Options, cfg *client.BackendConfig) error {
	if cfg.ServiceName != "" {
		opts.ServiceName = cfg.ServiceName
	}
	if cfg.Target != "" {
		opts.Target = cfg.Target
		return nil
	}
	return nil
}

func loadClientFilterConfig(opts *client.Options, key string) error {
	cfg, ok := client.DefaultClientConfig()[key]
	if !ok {
		return nil
	}
	for _, filterName := range cfg.Filter {
		f := filter.GetClient(filterName)
		if f == nil {
			return fmt.Errorf("client config: filter %s no registered", filterName)
		}
		opts.Filters = append(opts.Filters, f)
	}
	return nil
}
