load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "filters",
    srcs = ["filters.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/database/goredis/filters",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/client",
        "//backend/common/rpc/framework/codec",
        "//backend/common/rpc/framework/filter",
        "//backend/common/rpc/framework/transport",
    ],
)
