load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "options",
    srcs = [
        "options.go",
        "scheme.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/database/goredis/options",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/client",
        "//backend/common/rpc/framework/codec",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/http",
        "//backend/common/rpc/framework/transport",
        "@com_github_redis_go_redis_v9//:go-redis",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)
