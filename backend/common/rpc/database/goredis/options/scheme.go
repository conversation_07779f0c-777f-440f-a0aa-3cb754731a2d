package options

import (
	"strings"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
)

// parseScheme parses address.
func parseScheme(scheme, host string) ([]string, error) {
	var addrs []string
	switch scheme {
	case "ip", RedisSelectorName, RedissSelectorName:
		addrs = parseRedisScheme(host)
	default:
		return nil, errs.Newf(RetParseConfigFail, "scheme %s not support", scheme)
	}
	if len(addrs) == 0 {
		return nil, errs.Newf(RetParseConfigFail, "parseScheme addrs empty")
	}
	return addrs, nil
}

// parseIPScheme is parsing IP patterns.
func parseRedisScheme(host string) []string {
	rawAddrs := strings.Split(host, ",")
	addrs := make([]string, 0, len(rawAddrs))
	for _, rawAddr := range rawAddrs {
		addr := strings.TrimSpace(rawAddr)
		if addr != "" {
			addrs = append(addrs, addr)
		}
	}
	return addrs
}
