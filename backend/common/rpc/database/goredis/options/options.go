// Package options is redis configuration parameters.
package options

import (
	"crypto/tls"
	"net/url"
	"runtime"
	"strconv"
	"strings"
	"time"

	redis "github.com/redis/go-redis/v9"
	_ "go.uber.org/automaxprocs"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/client"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport"
)

// Constant.
const (
	RetParseConfigFail   = 30012 // Configuration parsing failed.
	MaxConnectionsPreCPU = 250   // The maximum number of connections to a single cpu core.

	RedisSelectorName  = "redis"  // redis
	RedissSelectorName = "rediss" // redis security.
)

// Options is redis configuration parameters.
type Options struct {
	Namespace string // Namespaces.
	Service   string // Called service name.

	rpcOption   *client.Options
	RedisOption *redis.UniversalOptions
	QueryOption *QueryOptions
}

// QueryOptions url query fields。
type QueryOptions struct {
	// Whether to enable context timeout, it is enabled by default.
	ContextTimeoutEnabled bool `json:"context_timeout_enabled,omitempty"`
	// Sentry mode, host name. The sentinel master name. Only failover clients.
	MasterName string `json:"master_name,omitempty"`
	// will execute the `CLIENT SETNAME ClientName` command for each conn.
	ClientName string `json:"client_name,omitempty"`
	// Maximum number of retries before giving up.
	// Default is 3 retries; -1 (not 0) disables retries.
	MaxRetries int64 `json:"max_retries,omitempty"`
	// Minimum backoff between each retry.
	// Default is 8 milliseconds; -1 disables backoff.
	// In milliseconds.
	MinRetryBackoff int64 `json:"min_retry_backoff,omitempty"`
	// Maximum backoff between each retry.
	// Default is 512 milliseconds; -1 disables backoff.
	// In milliseconds.
	MaxRetryBackoff int64 `json:"max_retry_backoff,omitempty"`
	// Dial timeout for establishing new connections.
	// Default is 5 seconds.
	// In milliseconds.
	DialTimeout int64 `json:"dial_timeout,omitempty"`
	// Timeout for socket reads. If reached, commands will fail
	// with a timeout instead of blocking. Supported values:
	//   - `0` - default timeout (3 seconds).
	//   - `-1` - no timeout (block indefinitely).
	//   - `-2` - disables SetReadDeadline calls completely.
	//
	// In milliseconds.
	ReadTimeout int64 `json:"read_timeout,omitempty"`
	// Timeout for socket writes. If reached, commands will fail
	// with a timeout instead of blocking.  Supported values:
	//   - `0` - default timeout (3 seconds).
	//   - `-1` - no timeout (block indefinitely).
	//   - `-2` - disables SetWriteDeadline calls completely.
	//
	// In milliseconds.
	WriteTimeout int64 `json:"write_timeout,omitempty"`
	// Type of connection pool.
	// true for FIFO pool, false for LIFO pool.
	// Note that FIFO has slightly higher overhead compared to LIFO,
	// but it helps closing idle connections faster reducing the pool size.
	PoolFifo bool `json:"pool_fifo,omitempty"`
	// Maximum number of socket connections.
	// Default is 250 connections per every available CPU as reported by runtime.GOMAXPROCS.
	PoolSize int64 `json:"pool_size,omitempty"`
	// Amount of time client waits for connection if all connections
	// are busy before returning an error.
	// Default is ReadTimeout + 1 second.
	PoolTimeout int64 `json:"pool_timeout,omitempty"`
	// Minimum number of idle connections which is useful when establishing
	// new connection is slow.
	MinIdleConns int64 `json:"min_idle_conns,omitempty"`
	// Maximum number of idle connections.
	MaxIdleConns int64 `json:"max_idle_conns,omitempty"`
	// ConnMaxIdleTime is the maximum amount of time a connection may be idle.
	// Should be less than server's timeout.
	//
	// Expired connections may be closed lazily before reuse.
	// If d <= 0, connections are not closed due to a connection's idle time.
	//
	// Default is 30 minutes. -1 disables idle timeout check.
	// In milliseconds.
	ConnMaxIdleTime int64 `json:"conn_max_idle_time,omitempty"`
	// ConnMaxLifetime is the maximum amount of time a connection may be reused.
	//
	// Expired connections may be closed lazily before reuse.
	// If <= 0, connections are not closed due to a connection's age.
	//
	// Default is to not close idle connections.
	ConnMaxLifetime int64 `json:"conn_max_lifetime,omitempty"`
	// The maximum number of retries before giving up. Command is retried
	// on network errors and MOVED/ASK redirects.
	// Default is 3 retries.
	MaxRedirects int64 `json:"max_redirects,omitempty"`
	// Enables read-only commands on slave nodes.
	ReadOnly bool `json:"read_only,omitempty"`
	// Allows routing read-only commands to the closest master or slave node.
	// It automatically enables ReadOnly.
	RouteByLatency bool `json:"route_by_latency,omitempty"`
	// Allows routing read-only commands to the random master or slave node.
	// It automatically enables ReadOnly.
	RouteRandomly bool `json:"route_randomly,omitempty"`
	// Enables TLS support.
	// Default is false.
	TLS bool `json:"tls,omitempty"`
}

// New handles configuration parameter parsing.
// redis.ParseURL: https://github.com/redis/go-redis/blob/v9.0.2/options.go#L222
// redis.ParseClusterURL: https://github.com/redis/go-redis/blob/v9.0.2/cluster.go#L137
func New(rpcOption *client.Options) (*Options, error) {
	o := &Options{
		rpcOption: rpcOption,
	}
	u, err := url.Parse(rpcOption.Target)
	if err != nil {
		return nil, errs.Wrapf(err, RetParseConfigFail, "url.Parse fail %v", err)
	}
	o.RedisOption, o.QueryOption, err = newOptions(u.RawQuery)
	if err != nil {
		return nil, err
	}
	if o.Service, err = parseURI(o.RedisOption, u); err != nil {
		return nil, err
	}
	// Fix default field values.
	if err = o.fixRedisOptions(u.RawQuery); err != nil {
		return nil, err
	}
	return o, nil
}

// fixRedisOptions fixes redis.Options defaults.
func (o *Options) fixRedisOptions(rawQuery string) error {
	q, err := url.ParseQuery(rawQuery)
	if err != nil {
		return errs.Wrapf(err, RetParseConfigFail, "url.ParseQuery fail %v", err)
	}
	r := o.RedisOption
	if !q.Has("context_timeout_enabled") {
		r.ContextTimeoutEnabled = true
	}
	if !q.Has("pool_size") {
		r.PoolSize = int(calcPoolSize())
	}
	if !q.Has("dial_timeout") {
		r.DialTimeout = o.rpcOption.Timeout
	}
	if !q.Has("read_timeout") {
		r.ReadTimeout = o.rpcOption.Timeout
	}
	if !q.Has("write_timeout") {
		r.WriteTimeout = o.rpcOption.Timeout
	}
	// use rpc password instead.
	if r.Password == "" {
		r.Password = parseTRPCPassword(o.rpcOption.CallOptions)
	}
	return nil
}

func newOptions(rawQuery string) (*redis.UniversalOptions, *QueryOptions, error) {
	queryOptions, err := newQueryOptions(rawQuery)
	if err != nil {
		return nil, nil, err
	}
	// Create RedisOption from queryOptions.
	redisOption := newRedisOptions(queryOptions)
	return redisOption, queryOptions, nil
}

// newQueryOptions creates a new QueryOptions。
func newQueryOptions(rawQuery string) (*QueryOptions, error) {
	// Parse the url query field.
	queryOptions := &QueryOptions{}
	if err := codec.Unmarshal(codec.SerializationTypeQuery, []byte(rawQuery), queryOptions); err != nil {
		return nil, err
	}
	return queryOptions, nil

}

// newRedisOptions creates a new redis.Options。
func newRedisOptions(q *QueryOptions) *redis.UniversalOptions {
	return &redis.UniversalOptions{
		// redis.Options field：https://github.com/redis/go-redis/blob/v9.0.2/options.go#L425
		ClientName:      q.ClientName,
		MaxRetries:      int(q.MaxRetries),
		MinRetryBackoff: fixDuration(q.MinRetryBackoff),
		MaxRetryBackoff: fixDuration(q.MaxRetryBackoff),
		DialTimeout:     fixDuration(q.DialTimeout),
		ReadTimeout:     fixDuration(q.ReadTimeout),
		WriteTimeout:    fixDuration(q.WriteTimeout),
		PoolFIFO:        q.PoolFifo,
		PoolSize:        int(q.PoolSize),
		PoolTimeout:     fixDuration(q.PoolTimeout),
		MinIdleConns:    int(q.MinIdleConns),
		MaxIdleConns:    int(q.MaxIdleConns),
		ConnMaxIdleTime: fixDuration(q.ConnMaxIdleTime),
		ConnMaxLifetime: fixDuration(q.ConnMaxLifetime),
		// redis.ClusterOptions field：https://github.com/redis/go-redis/blob/v9.0.2/cluster.go#L215
		MaxRedirects:   int(q.MaxRedirects),
		ReadOnly:       q.ReadOnly,
		RouteByLatency: q.RouteByLatency,
		RouteRandomly:  q.RouteRandomly,
		// Expanded fields.
		MasterName:            q.MasterName,
		ContextTimeoutEnabled: q.ContextTimeoutEnabled,
		TLSConfig:             getTLSConfig(q.TLS),
	}
}

// parseURI is the filled uri field.
func parseURI(redisOptions *redis.UniversalOptions, u *url.URL) (string, error) {
	if u.User != nil {
		if redisOptions.Username == "" {
			redisOptions.Username = u.User.Username()
		}
		if redisOptions.Password == "" {
			redisOptions.Password, _ = u.User.Password()
		}
	}
	if redisOptions.DB == 0 {
		if strings.Index(u.Path, "/") == 0 {
			db, err := strconv.ParseInt(u.Path[1:], 10, 64)
			if err != nil {
				return "", errs.Wrapf(err, RetParseConfigFail, "u.Path '%s' invalid %v", u.Path, err)
			}
			redisOptions.DB = int(db)
		}
	}
	var err error
	if redisOptions.Addrs, err = parseScheme(u.Scheme, u.Host); err != nil {
		return "", err
	}
	return u.Host, nil
}

// parseTRPCPassword parses the rpc password field.
func parseTRPCPassword(callOptions []transport.RoundTripOption) string {
	roundTripOptions := &transport.RoundTripOptions{}
	for _, option := range callOptions {
		option(roundTripOptions)
	}
	// Add url decode to the password,
	// if the decoding fails, use the old url directly.
	password, _ := urlUnescape(roundTripOptions.Password)
	return password
}

// fixDuration fix time interval.
func fixDuration(ms int64) time.Duration {
	// special meaning
	if ms <= 0 {
		return time.Duration(ms)
	}
	return time.Duration(ms) * time.Millisecond
}

func getTLSConfig(useTLS bool) *tls.Config {
	if useTLS {
		return &tls.Config{
			InsecureSkipVerify: true,
		}
	}
	return nil
}

// calcPoolSize recalculates the pool size.
// 1 The default number is 10, and the v4 machine only has 40 connections,
// which does not conform to the actual production environment,
// https://github.com/redis/go-redis/blob/v9.0.2/options.go#L98
// 2 Numbers are based on the definition of online stress testing: v4 machine 1000, v8 machine 2000;
// 3 No adjustment phenomenon: the time consumption of a single request will expand (for example: 10ms -> 100ms),
// and a large number of requests are blocked in places waiting to obtain connections;
func calcPoolSize() int64 {
	cpuCore := runtime.GOMAXPROCS(0)
	if cpuCore <= 0 {
		return 0
	}
	return int64(cpuCore) * MaxConnectionsPreCPU
}

// urlUnescape is url decode。
func urlUnescape(rawURL string) (string, error) {
	decodeURL, err := url.QueryUnescape(rawURL)
	if err != nil {
		return rawURL, err
	}
	return decodeURL, nil
}
