client:
  service:
    - callee: moego.redis.test
      # Request service address format： redis://<user>:<password>@<host>:<port>/<db_number>, for details, please refer to：https://github.com/redis/go-redis/blob/fba6dececdf138af0b34928e28d514f9205ed7d2/options.go#L257
      target: redis://redis.t2.moego.dev:40179/0?min_idle_conns=10&tls=true
      password: iMoReGoTdesstingeCache250310_7fec987d
      timeout: 10000
