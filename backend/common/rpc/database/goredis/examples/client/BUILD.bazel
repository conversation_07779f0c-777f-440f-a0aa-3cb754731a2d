load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "client_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/database/goredis/examples/client",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/goredis",
        "//backend/common/rpc/framework",
    ],
)

go_binary(
    name = "client",
    embed = [":client_lib"],
    visibility = ["//visibility:public"],
)
