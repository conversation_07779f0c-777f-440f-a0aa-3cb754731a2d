// Package main is the main package.
// Used to test server consumption of multiple messages
package main

import (
	"context"
	"fmt"
	"time"

	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/database/goredis"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework"
)

func main() {
	framework.ServerConfigPath = "/Users/<USER>/moego/moego/backend/common/rpc/database/goredis/examples/client/config.yaml"
	framework.NewServer()
	cli, err := goredis.New("moego.redis.test")
	if err != nil {
		fmt.Println(err)
	}
	key := "ark"
	value := "sb"
	if err := cli.Set(context.Background(), key, value, time.Minute); err != nil {
		fmt.Println(err)
	}
	res := cli.Get(context.Background(), key)
	if res.Err() != nil {
		fmt.Println(res.Err())
	}
	fmt.Println(res.String())
}
