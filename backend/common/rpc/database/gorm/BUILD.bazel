load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "gorm",
    srcs = [
        "client.go",
        "codec.go",
        "plugin.go",
        "transport.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/database/gorm/serializer",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/client",
        "//backend/common/rpc/framework/codec",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/common/rpc/framework/plugin",
        "//backend/common/rpc/framework/transport",
        "@com_github_bytedance_sonic//:sonic",
        "@io_gorm_driver_mysql//:mysql",
        "@io_gorm_driver_postgres//:postgres",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//schema",
        "@org_golang_google_grpc//codes",
    ],
)
