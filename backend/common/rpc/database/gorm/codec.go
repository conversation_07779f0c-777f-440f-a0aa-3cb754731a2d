package gorm

import (
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
)

func init() {
	codec.Register("gorm", nil, DefaultClientCodec)
}

var (
	DefaultClientCodec = &ClientCodec{}
)

type ClientCodec struct{}

func (c *ClientCodec) Encode(msg codec.Msg, body []byte) (buffer []byte, err error) {
	return nil, nil
}

func (c *ClientCodec) Decode(msg codec.Msg, buffer []byte) (body []byte, err error) {
	return nil, nil
}
