package gorm

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"sync"
	"time"

	"google.golang.org/grpc/codes"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport"
)

func init() {
	transport.RegisterClientTransport("gorm", defaultClientTransport)
}

type PoolConfig struct {
	MaxIdle     int
	MaxOpen     int
	MaxLifetime time.Duration
	DriverName  string
}

type ClientTransport struct {
	opener            func(driverName, dataSourceName string) (*sql.DB, error)
	opts              *transport.ClientTransportOptions
	SQLDB             map[string]*sql.DB
	SQLDBLock         sync.RWMutex
	DefaultPoolConfig PoolConfig
	PoolConfigs       map[string]PoolConfig
}

var defaultClientTransport = NewClientTransport()

func NewClientTransport(opt ...transport.ClientTransportOption) *ClientTransport {
	opts := &transport.ClientTransportOptions{}
	for _, o := range opt {
		o(opts)
	}
	return &ClientTransport{
		opener: sql.Open,
		opts:   opts,
		SQLDB:  make(map[string]*sql.DB),
		DefaultPoolConfig: PoolConfig{
			MaxIdle:     10,
			MaxOpen:     1000,
			MaxLifetime: 3 * time.Minute,
		},
	}
}

func (ct *ClientTransport) RoundTrip(ctx context.Context, reqBuf []byte,
	callOpts ...transport.RoundTripOption) (rspBuf []byte, err error) {
	msg := codec.Message(ctx)

	req, ok := msg.ClientReqHead().(*Request)
	if !ok {
		return nil, errs.NewFrameError(codes.DataLoss,
			"sql client transport: ReqHead should be type of *gormCli.Request")
	}
	rsp, ok := msg.ClientRspHead().(*Response)
	if !ok {
		return nil, errs.NewFrameError(codes.DataLoss,
			"sql client transport: RspHead should be type of *gormCli.Response")
	}
	if req.Tx != nil {
		err = runTxCommand(ctx, req.Tx, req, rsp)
		return
	}

	sqlOpts := &transport.RoundTripOptions{}
	for _, o := range callOpts {
		o(sqlOpts)
	}

	db, err := ct.GetDB(msg.CalleeServiceName(), sqlOpts.Address)
	if err != nil {
		err = fmt.Errorf(
			`err: %w, 
current masked sqlOpts.Address: %s,
if it is not what you want, it is possible that your client config is not loaded correctly`,
			err, sqlOpts.Address)
		return
	}
	err = runCommand(ctx, db, req, rsp)
	return
}

func runTxCommand(ctx context.Context, tx *sql.Tx, req *Request, rsp *Response) error {
	switch req.Op {
	case OpPrepareContext:
		stmt, err := tx.PrepareContext(ctx, req.Query)
		if err != nil {
			return err
		}
		rsp.Stmt = stmt
	case OpExecContext:
		result, err := tx.ExecContext(ctx, req.Query, req.Args...)
		if err != nil {
			return err
		}
		rsp.Result = result
	case OpQueryContext:
		rows, err := tx.QueryContext(ctx, req.Query, req.Args...)
		if err != nil {
			return err
		}
		rsp.Rows = rows
	case OpQueryRowContext:
		row := tx.QueryRowContext(ctx, req.Query, req.Args...)
		rsp.Row = row
	case OpCommit:
		err := tx.Commit()
		if err != nil {
			return err
		}
	case OpRollback:
		err := tx.Rollback()
		if err != nil {
			return err
		}
	default:
		return errs.NewFrameError(codes.InvalidArgument, "Illegal Method")
	}
	return nil
}

func runCommand(ctx context.Context, db *sql.DB, req *Request, rsp *Response) error {
	switch req.Op {
	case OpPrepareContext:
		stmt, err := db.PrepareContext(ctx, req.Query)
		if err != nil {
			return err
		}
		rsp.Stmt = stmt
	case OpExecContext:
		result, err := db.ExecContext(ctx, req.Query, req.Args...)
		if err != nil {
			return err
		}
		rsp.Result = result
	case OpQueryContext:
		rows, err := db.QueryContext(ctx, req.Query, req.Args...)
		if err != nil {
			return err
		}
		rsp.Rows = rows
	case OpQueryRowContext:
		row := db.QueryRowContext(ctx, req.Query, req.Args...)
		rsp.Row = row
	case OpBeginTx:
		tx, err := db.BeginTx(ctx, req.TxOptions)
		if err != nil {
			return err
		}
		rsp.Tx = tx
	case OpPing:
		return db.Ping()
	case OpGetDB:
		rsp.DB = db
		return nil
	default:
		return errs.NewFrameError(codes.InvalidArgument, "Illegal Method")
	}
	return nil
}

func (ct *ClientTransport) GetDB(serviceName, dsn string) (*sql.DB, error) {
	ct.SQLDBLock.RLock()
	db, ok := ct.SQLDB[dsn]
	ct.SQLDBLock.RUnlock()
	if ok {
		return db, nil
	}
	ct.SQLDBLock.Lock()
	defer ct.SQLDBLock.Unlock()

	db, ok = ct.SQLDB[dsn]
	if ok {
		return db, nil
	}
	db, err := ct.initDB(serviceName, dsn)
	if err != nil {
		return nil, wrapperSQLOpenError(err)
	}
	poolConfig, ok := ct.PoolConfigs[serviceName]
	if !ok {
		poolConfig = ct.DefaultPoolConfig
	}
	if poolConfig.MaxIdle > 0 {
		db.SetMaxIdleConns(poolConfig.MaxIdle)
	}
	if poolConfig.MaxOpen > 0 {
		db.SetMaxOpenConns(poolConfig.MaxOpen)
	}
	if poolConfig.MaxLifetime > 0 {
		db.SetConnMaxLifetime(poolConfig.MaxLifetime)
	}

	ct.SQLDB[dsn] = db
	return db, nil
}

func (ct *ClientTransport) initDB(s, dsn string) (*sql.DB, error) {
	if conf, ok := ct.PoolConfigs[s]; ok && conf.DriverName != "" {
		return ct.opener(conf.DriverName, dsn)
	}
	splitServiceName := strings.Split(s, ".")
	if len(splitServiceName) < 1 {
		return ct.opener("mysql", dsn)
	}
	dbEngineType := splitServiceName[0]
	switch dbEngineType {
	case "postgres":
		return ct.opener("pgx", dsn)
	default:
		return ct.opener("mysql", dsn)
	}
}

func wrapperSQLOpenError(err error) error {
	errStr := err.Error()
	if strings.HasPrefix(errStr, "sql: unknown driver") {
		return fmt.Errorf("error: %s, should register before open driver,"+
			"please refer: https://pkg.go.dev/database/sql#Register", errStr)
	}
	return err
}
