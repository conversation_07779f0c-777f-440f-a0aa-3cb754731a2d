package serializer

import (
	"context"
	"fmt"
	"reflect"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/runtime/protoimpl"
	"gorm.io/gorm/schema"
)

// ProtoEnumSerializer 用于处理 proto enum 类型的字段和数据库 string/[]byte 类型的字段的序列化/反序列化
//
// 用法: 在 field 后追加 gorm:"serializer:proto_enum"
//
//	type Example struct {
//		Status proto.Status `gorm:"serializer:proto_enum"`
//	}
type ProtoEnumSerializer struct {
}

var enumType = reflect.TypeFor[protoreflect.Enum]()

func (s *ProtoEnumSerializer) Scan(ctx context.Context, field *schema.Field, dst reflect.Value, dbValue any) error {
	var enumFromDB any
	if dbValue != nil {
		switch v := dbValue.(type) {
		case []byte:
			enumFromDB = string(v)
		case string:
			enumFromDB = v
		case int64:
			enumFromDB = v
		default:
			return status.Error(codes.InvalidArgument, "invalid db value for proto enum")
		}
	}
	if field.FieldType.Implements(enumType) {
		enum, ok := reflect.New(field.FieldType).Interface().(protoreflect.Enum)
		if !ok {
			return status.Error(codes.InvalidArgument, "field type is not proto enum")
		}

		var vd protoreflect.EnumValueDescriptor
		switch ev := enumFromDB.(type) {
		case string:
			vd = enum.Descriptor().Values().ByName(protoreflect.Name(ev))
		case int64:
			vd = enum.Descriptor().Values().ByNumber(protoreflect.EnumNumber(ev))
		}

		if vd == nil {
			return fmt.Errorf("unknown value %q for enum %s", enumFromDB, enum.Descriptor().FullName())
		}

		field.ReflectValueOf(ctx, dst).Set(reflect.ValueOf(vd.Number()).Convert(field.FieldType))
		return nil
	}
	return status.Error(codes.InvalidArgument, "db field type can not be converted to proto enum")
}

func (s *ProtoEnumSerializer) Value(_ context.Context, _ *schema.Field, _ reflect.Value, fieldValue any) (any, error) {
	if enum, ok := fieldValue.(protoreflect.Enum); ok {
		return protoimpl.X.EnumStringOf(enum.Descriptor(), enum.Number()), nil
	}
	return nil, status.Error(codes.InvalidArgument, "field type is not proto enum")
}
