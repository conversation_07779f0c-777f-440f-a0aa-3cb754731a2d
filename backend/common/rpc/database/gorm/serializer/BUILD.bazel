load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "serializer",
    srcs = [
        "proto_enum.go",
        "proto_json.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm/serializer",
    visibility = ["//visibility:public"],
    deps = [
        "@io_gorm_gorm//schema",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//reflect/protoreflect",
        "@org_golang_google_protobuf//runtime/protoimpl",
    ],
)
