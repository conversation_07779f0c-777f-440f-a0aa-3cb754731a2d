package serializer

import (
	"context"
	"reflect"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm/schema"
)

// ProtoJSONSerializer 用于处理 proto json 类型的字段和数据库 string/[]byte 类型的字段的序列化/反序列化
// 一般不推荐在数据库中存储 json 类型的字段，但有些需要存储 protobuf one of 类型的结构，可以使用该 serializer
//
// 用法: 在 field 后追加 gorm:"serializer:proto_json"
//
//	type Example struct {
//		Message proto.OneOfMessage `gorm:"serializer:proto_json"`
//	}
type ProtoJSONSerializer struct{}

var pbMsgType = reflect.TypeFor[proto.Message]()

func (s *ProtoJSONSerializer) Scan(ctx context.Context, field *schema.Field, dst reflect.Value, dbValue any) error {
	var pbJSON []byte
	if dbValue != nil {
		switch v := dbValue.(type) {
		case []byte:
			pbJSON = v
		case string:
			pbJSON = []byte(v)
		default:
			return status.Error(codes.InvalidArgument, "invalid db value for proto message")
		}
	}
	if field.FieldType.Implements(pbMsgType) {
		pb, ok := reflect.New(field.FieldType.Elem()).Interface().(proto.Message)
		if !ok {
			return status.Error(codes.InvalidArgument, "field type is not proto message")
		}
		if err := (protojson.UnmarshalOptions{
			DiscardUnknown: true,
		}).Unmarshal(pbJSON, pb); err != nil {
			return status.Error(codes.InvalidArgument, "failed to unmarshal proto JSON value")
		}
		field.ReflectValueOf(ctx, dst).Set(reflect.ValueOf(pb))
		return nil
	}
	return status.Error(codes.InvalidArgument, "db field type can not be converted to proto message")
}

func (s *ProtoJSONSerializer) Value(_ context.Context, _ *schema.Field, _ reflect.Value, fieldValue any) (
	any, error) {
	// check if fieldValue is not a pointer, and if so, create a new pointer to the value
	if reflect.TypeOf(fieldValue).Kind() != reflect.Ptr {
		ptr := reflect.New(reflect.TypeOf(fieldValue))
		ptr.Elem().Set(reflect.ValueOf(fieldValue))
		fieldValue = ptr.Interface()
	}
	if pbMsg, ok := fieldValue.(proto.Message); ok {
		return protojson.Marshal(pbMsg)
	}
	return nil, status.Error(codes.InvalidArgument, "field type is not proto message")
}
