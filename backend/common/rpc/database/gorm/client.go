package gorm

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"fmt"
	"reflect"
	"strings"
	"unsafe"

	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/client"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
)

type OpEnum int

const (
	OpPrepareContext OpEnum = iota + 1
	OpExecContext
	OpQueryContext
	OpQueryRowContext
	OpBeginTx
	OpPing
	OpCommit
	OpRollback
	OpGetDB
)

func (op OpEnum) String() string {
	return [...]string{
		"",
		"PrepareContext",
		"ExecContext",
		"QueryContext",
		"QueryRowContext",
		"BeginTx",
		"<PERSON>",
		"Commit",
		"Rollback",
		"GetDB",
	}[op]
}

type ConnPool interface {
	PrepareContext(ctx context.Context, query string) (*sql.Stmt, error)
	ExecContext(ctx context.Context, query string, args ...interface{}) (sql.Result, error)
	QueryContext(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error)
	QueryRowContext(ctx context.Context, query string, args ...interface{}) *sql.Row
	BeginTx(ctx context.Context, opts *sql.TxOptions) (gorm.ConnPool, error)
	Ping() error
	GetDBConn() (*sql.DB, error)
}

type Request struct {
	Op        OpEnum
	Query     string
	Args      []interface{}
	Tx        *sql.Tx
	TxOptions *sql.TxOptions
}

type Response struct {
	Result sql.Result
	Stmt   *sql.Stmt
	Row    *sql.Row
	Rows   *sql.Rows
	Tx     *sql.Tx
	DB     *sql.DB
}

type Client struct {
	ServiceName string
	Client      client.Client
	opts        []client.Option
}

type TxClient struct {
	Client *Client
	Tx     *sql.Tx
}

func NewConnPool(name string, opts ...client.Option) ConnPool {
	c := &Client{
		ServiceName: name,
		Client:      client.DefaultClient,
	}
	c.opts = make([]client.Option, 0, len(opts)+3)
	c.opts = append(c.opts, opts...)
	c.opts = append(c.opts,
		client.WithProtocol("gorm"),
		client.WithTimeout(0),
	)

	return c
}

func NewClientProxy(name string, opts ...client.Option) (*gorm.DB, error) {
	connPool := NewConnPool(name, opts...)
	splitServiceName := strings.Split(name, ".")
	if len(splitServiceName) < 1 {
		return gorm.Open(
			mysql.New(
				mysql.Config{
					Conn: connPool,
				},
			),
			&gorm.Config{
				SkipDefaultTransaction: true,
				QueryFields:            true,
			},
		)
	}

	dbEngineType := splitServiceName[0]
	switch dbEngineType {
	case "postgres":
		return gorm.Open(
			postgres.New(
				postgres.Config{
					PreferSimpleProtocol: true,
					Conn:                 connPool,
				},
			),
			&gorm.Config{
				SkipDefaultTransaction: true,
				QueryFields:            true,
			},
		)
	}

	return gorm.Open(
		mysql.New(
			mysql.Config{
				Conn: connPool,
			},
		),
		&gorm.Config{
			SkipDefaultTransaction: true,
			QueryFields:            true,
		},
	)
}

func handleReq(ctx context.Context, cp gorm.ConnPool, mreq *Request, mrsp *Response) error {
	var gc *Client
	if txgc, ok := cp.(*TxClient); ok {
		gc = txgc.Client
		mreq.Tx = txgc.Tx
	} else {
		gc = cp.(*Client)
	}
	mctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName(fmt.Sprintf("/%s/%s", gc.ServiceName, mreq.Op))
	msg.WithCalleeServiceName(gc.ServiceName)
	msg.WithSerializationType(-1)
	msg.WithCompressType(codec.CompressTypeNoop)
	msg.WithClientReqHead(mreq)
	msg.WithClientRspHead(mrsp)

	err := handleReqArgs(mreq)
	if err != nil {
		return err
	}
	return gc.Client.Invoke(mctx, mreq, mrsp, gc.opts...)
}

func handleReqArgs(mreq *Request) error {
	for k, arg := range mreq.Args {
		if valuer, ok := arg.(driver.Valuer); ok {
			v, err := valuer.Value()
			if err != nil {
				return err
			}
			mreq.Args[k] = v
		}
	}
	return nil
}

func (gc *Client) PrepareContext(ctx context.Context, query string) (*sql.Stmt, error) {
	return prepareContext(gc, ctx, query)
}

func prepareContext(cp gorm.ConnPool, ctx context.Context, query string) (*sql.Stmt, error) {
	mreq := &Request{
		Op:    OpPrepareContext,
		Query: query,
	}
	mrsp := &Response{}

	if err := handleReq(ctx, cp, mreq, mrsp); err != nil {
		return nil, err
	}
	return mrsp.Stmt, nil
}

func (gc *Client) ExecContext(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	return execContext(gc, ctx, query, args...)
}

func execContext(cp gorm.ConnPool, ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	mreq := &Request{
		Op:    OpExecContext,
		Query: query,
		Args:  args,
	}
	mrsp := &Response{}

	if err := handleReq(ctx, cp, mreq, mrsp); err != nil {
		return nil, err
	}
	return mrsp.Result, nil
}

func (gc *Client) QueryContext(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	return queryContext(gc, ctx, query, args...)
}

func queryContext(cp gorm.ConnPool, ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	mreq := &Request{
		Op:    OpQueryContext,
		Query: query,
		Args:  args,
	}
	mrsp := &Response{}

	if err := handleReq(ctx, cp, mreq, mrsp); err != nil {
		return nil, err
	}
	return mrsp.Rows, nil
}

func (gc *Client) QueryRowContext(ctx context.Context, query string, args ...interface{}) *sql.Row {
	return queryRowContext(gc, ctx, query, args...)
}

func queryRowContext(cp gorm.ConnPool, ctx context.Context, query string, args ...interface{}) *sql.Row {
	mreq := &Request{
		Op:    OpQueryRowContext,
		Query: query,
		Args:  args,
	}
	mrsp := &Response{}
	if err := handleReq(ctx, cp, mreq, mrsp); err != nil {
		row := &sql.Row{}
		v := reflect.ValueOf(row)
		errField := v.Elem().FieldByName("err")
		errPointer := unsafe.Pointer(errField.UnsafeAddr())
		errVal := (*error)(errPointer)
		*errVal = err
		return row
	}
	return mrsp.Row
}

func (gc *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (gorm.ConnPool, error) {
	mreq := &Request{
		Op:        OpBeginTx,
		TxOptions: opts,
	}
	mrsp := &Response{}

	if err := handleReq(ctx, gc, mreq, mrsp); err != nil {
		return nil, err
	}

	txc := &TxClient{
		Client: gc,
		Tx:     mrsp.Tx,
	}
	return txc, nil
}

func (gc *Client) Ping() error {
	mreq := &Request{
		Op: OpPing,
	}
	mrsp := &Response{}
	ctx := rpc.BackgroundContext()
	if err := handleReq(ctx, gc, mreq, mrsp); err != nil {
		return err
	}
	return nil
}

func (gc *Client) GetDBConn() (*sql.DB, error) {
	mreq := &Request{
		Op: OpGetDB,
	}
	mrsp := &Response{}
	ctx := rpc.BackgroundContext()
	if err := handleReq(ctx, gc, mreq, mrsp); err != nil {
		return nil, err
	}
	return mrsp.DB, nil
}

func (txgc *TxClient) PrepareContext(ctx context.Context, query string) (*sql.Stmt, error) {
	return prepareContext(txgc, ctx, query)
}

func (txgc *TxClient) ExecContext(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	return execContext(txgc, ctx, query, args...)
}

func (txgc *TxClient) QueryContext(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	return queryContext(txgc, ctx, query, args...)
}

func (txgc *TxClient) QueryRowContext(ctx context.Context, query string, args ...interface{}) *sql.Row {
	return queryRowContext(txgc, ctx, query, args...)
}

func (txgc *TxClient) Commit() error {
	mreq := &Request{
		Op: OpCommit,
	}
	mrsp := &Response{}

	return handleReq(context.TODO(), txgc, mreq, mrsp)
}

func (txgc *TxClient) Rollback() error {
	mreq := &Request{
		Op: OpRollback,
	}
	mrsp := &Response{}

	return handleReq(context.TODO(), txgc, mreq, mrsp)
}
