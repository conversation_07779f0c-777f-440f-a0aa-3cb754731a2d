package gorm

import (
	"time"

	"github.com/bytedance/sonic"
	"gorm.io/gorm/schema"

	"github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm/serializer"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/plugin"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport"
)

const (
	pluginType = "database"
	pluginName = "gorm"
)

func init() {
	plugin.Register(pluginName, &Plugin{})
	schema.RegisterSerializer("proto_enum", &serializer.ProtoEnumSerializer{})
	schema.RegisterSerializer("proto_json", &serializer.ProtoJSONSerializer{})
}

type Config struct {
	MaxIdle     int `yaml:"max_idle"`     // Maximum number of idle connections.
	MaxOpen     int `yaml:"max_open"`     // Maximum number of connections that can be open at same time.
	MaxLifetime int `yaml:"max_lifetime"` // The maximum lifetime of each connection, in milliseconds.
	Service     []struct {
		Name        string
		MaxIdle     int    `yaml:"max_idle"`
		MaxOpen     int    `yaml:"max_open"`
		MaxLifetime int    `yaml:"max_lifetime"`
		DriverName  string `yaml:"driver_name"`
	}
}

type Plugin struct{}

func (m *Plugin) Type() string {
	return pluginType
}

func (m *Plugin) Setup(name string, configDesc plugin.Decoder) (err error) {
	var config Config
	if err = configDesc.Decode(&config); err != nil {
		return
	}

	configStr, _ := sonic.MarshalString(config)
	log.Infof("gorm Plugin Credential config:%s", configStr)

	poolConfigs := make(map[string]PoolConfig, len(config.Service))
	for _, s := range config.Service {
		poolConfigs[s.Name] = PoolConfig{
			MaxIdle:     s.MaxIdle,
			MaxOpen:     s.MaxOpen,
			MaxLifetime: time.Duration(s.MaxLifetime) * time.Millisecond,
			DriverName:  s.DriverName,
		}
	}
	defaultClientTransport.PoolConfigs = poolConfigs

	defaultClientTransport.DefaultPoolConfig = PoolConfig{
		MaxIdle:     config.MaxIdle,
		MaxOpen:     config.MaxOpen,
		MaxLifetime: time.Duration(config.MaxLifetime) * time.Millisecond,
	}

	transport.RegisterClientTransport("gorm", defaultClientTransport)

	return nil
}
