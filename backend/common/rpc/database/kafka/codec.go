package kafka

import (
	"fmt"
	"os"
	"path"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
)

func init() {
	codec.Register("kafka", DefaultServerCodec, DefaultClientCodec)
}

var (
	DefaultServerCodec = &ServerCodec{}
	DefaultClientCodec = &ClientCodec{}

	serverName = path.Base(os.Args[0])
)

type ServerCodec struct{}

func (s *ServerCodec) Decode(_ codec.Msg, _ []byte) ([]byte, error) {
	return nil, nil
}

func (s *ServerCodec) Encode(_ codec.Msg, _ []byte) ([]byte, error) {
	return nil, nil
}

type ClientCodec struct{}

func (c *ClientCodec) Encode(kafkaMsg codec.Msg, _ []byte) ([]byte, error) {
	if kafkaMsg.CallerServiceName() == "" {
		kafkaMsg.WithCallerServiceName(fmt.Sprintf("rpc.kafka.%s.service", serverName))
	}
	return nil, nil
}

func (c *ClientCodec) Decode(kafkaMsg codec.Msg, _ []byte) ([]byte, error) {
	return nil, nil
}
