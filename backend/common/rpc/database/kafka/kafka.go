/*
Package kafka encapsulated from github.com/IBM/sarama
Producer sending through rpc.Client
Implement Consumer logic through rpc.Service
*/
package kafka

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/IBM/sarama"
	"golang.org/x/time/rate"
	"google.golang.org/grpc/codes"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport"
)

var ContinueWithoutCommitError = &errs.Error{
	Type: errs.ErrorTypeBusiness,
	Code: int32(codes.Unknown),
	Msg:  "Error:Continue to consume message without committing ack",
}

var Timeout = 2 * time.Second

func IsCWCError(err error) bool {
	return err == ContinueWithoutCommitError
}

var (
	serviceCloseError = errs.NewFrameError(codes.Internal, "kafka consumer service close")
	sessionCloseError = errs.NewFrameError(codes.Internal, "kafka consumer group session close")
	messageCloseError = errs.NewFrameError(codes.Internal, "kafka consumer group claim message close")
)

type Producer struct {
	topic         string
	async         bool
	asyncProducer sarama.AsyncProducer
	syncProducer  sarama.SyncProducer
	rpcMeta       bool
}

type singleConsumerHandler struct {
	opts          *transport.ListenServeOptions
	ctx           context.Context
	retryMax      int
	retryInterval time.Duration
	rpcMeta       bool
	limiter       *rate.Limiter
}

func (h *singleConsumerHandler) Setup(sarama.ConsumerGroupSession) error {
	if h.retryInterval == 0 {
		h.retryInterval = time.Millisecond
	}
	return nil
}

func (h *singleConsumerHandler) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

func (h *singleConsumerHandler) ConsumeClaim(sess sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for {
		if err := h.limiter.Wait(h.ctx); err != nil {
			if err == h.ctx.Err() {
				return serviceCloseError
			}
			return fmt.Errorf("kafka server transport: limiter error: %w", err)
		}
		select {
		case <-h.ctx.Done():
			return serviceCloseError
		case <-sess.Context().Done():
			return sessionCloseError
		case msg, ok := <-claim.Messages():
			if !ok {
				return messageCloseError
			}
			h.retryConsumeAndMark(sess, msg, claim)
		}
	}
}

type RawSaramaContext struct {
	Session sarama.ConsumerGroupSession
	Claim   sarama.ConsumerGroupClaim
}

type rawSaramaContextKey struct{}

func withRawSaramaContext(ctx context.Context, raw *RawSaramaContext) context.Context {
	return context.WithValue(ctx, rawSaramaContextKey{}, raw)
}

func GetRawSaramaContext(ctx context.Context) (*RawSaramaContext, bool) {
	rawContext, ok := ctx.Value(rawSaramaContextKey{}).(*RawSaramaContext)
	return rawContext, ok
}

func (h *singleConsumerHandler) retryConsumeAndMark(
	session sarama.ConsumerGroupSession,
	m *sarama.ConsumerMessage,
	claim sarama.ConsumerGroupClaim,
) {
	retryNum := 0
	for {
		ctx, rpcMsg := genTRPCMessage(m, h.opts.ServiceName, m.Topic, h.rpcMeta)
		ctx = withRawSaramaContext(ctx, &RawSaramaContext{Session: session, Claim: claim})

		_, err := h.opts.Handler.Handle(ctx, nil)
		rspErr := rpcMsg.ServerRspErr()

		if err == nil && rspErr == nil {
			break
		}

		msgInfo := fmt.Sprintf("%s:%d:%d", m.Topic, m.Partition, m.Offset)

		if IsCWCError(rspErr) {
			log.WarnContextf(ctx, "kafka consumer handle warn:%v, msg: %+v", rspErr, msgInfo)
			return
		}

		retryNum++
		log.ErrorContextf(ctx, "kafka consumer msg %s try time %d get fail:%v rspErr:%v, ", msgInfo, retryNum, err, rspErr)

		if h.retryMax != 0 && retryNum > h.retryMax {
			break
		}

		t := time.NewTimer(h.retryInterval)
		select {
		case <-h.ctx.Done():
			return
		case <-session.Context().Done():
			return
		case <-t.C:

		}
	}
	session.MarkMessage(m, "")
}

type batchConsumerHandler struct {
	opts *transport.ListenServeOptions
	ctx  context.Context

	maxNum        int
	flushInterval time.Duration
	retryMax      int
	retryInterval time.Duration
	rpcMeta       bool
	limiter       *rate.Limiter
}

func (h *batchConsumerHandler) Setup(sarama.ConsumerGroupSession) error {
	if h.retryInterval == 0 {
		h.retryInterval = time.Millisecond
	}
	return nil
}

func (h *batchConsumerHandler) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

func (h *batchConsumerHandler) ConsumeClaim(
	session sarama.ConsumerGroupSession,
	claim sarama.ConsumerGroupClaim,
) error {
	msgArray := make([]*sarama.ConsumerMessage, h.maxNum)
	idx := 0

	ticker := time.NewTicker(h.flushInterval)
	defer ticker.Stop()

	for {
		if err := h.limiter.Wait(h.ctx); err != nil {
			if err == h.ctx.Err() {
				return serviceCloseError
			}
			return fmt.Errorf("kafka server transport: limiter error: %w", err)
		}
		select {
		case <-h.ctx.Done():
			return serviceCloseError
		case <-session.Context().Done():
			return sessionCloseError
		case msg, ok := <-claim.Messages():
			if !ok {
				return messageCloseError
			}

			msgArray[idx] = msg
			idx++

			if idx >= h.maxNum {

				handleMsg := make([]*sarama.ConsumerMessage, len(msgArray))
				copy(handleMsg, msgArray)
				h.retryConsumeAndMark(session, claim, handleMsg...)
				idx = 0
			}
		case <-ticker.C:
			if idx > 0 {

				handleMsg := make([]*sarama.ConsumerMessage, idx)
				copy(handleMsg, msgArray[:idx])
				h.retryConsumeAndMark(session, claim, handleMsg...)
				idx = 0
			}
		}
	}
}

func (h *batchConsumerHandler) retryConsumeAndMark(
	session sarama.ConsumerGroupSession,
	claim sarama.ConsumerGroupClaim,
	msgs ...*sarama.ConsumerMessage,
) {
	retryNum := 0
	for {
		ctx, rpcMsg := genTRPCMessage(msgs, h.opts.ServiceName, msgs[0].Topic, h.rpcMeta)
		ctx = withRawSaramaContext(ctx, &RawSaramaContext{Session: session, Claim: claim})

		_, err := h.opts.Handler.Handle(ctx, nil)
		rspErr := rpcMsg.ServerRspErr()

		if err == nil && rspErr == nil {
			break
		}

		retryNum++
		offset := make([]string, len(msgs))
		for i, v := range msgs {
			offset[i] = strconv.Itoa(int(v.Offset))
		}
		msg := msgs[0]
		info := fmt.Sprintf("topic: %s partition: %d offset: %s", msg.Topic, msg.Partition, strings.Join(offset, ","))
		log.ErrorContextf(ctx, "kafka consumer %s try number %d err: %v  msgErr: %v, ", info, retryNum, err, rspErr)

		if h.retryMax != 0 && retryNum > h.retryMax {
			break
		}

		t := time.NewTimer(h.retryInterval)
		select {
		case <-h.ctx.Done():
			return
		case <-session.Context().Done():
			return
		case <-t.C:

		}
	}
	session.MarkMessage(msgs[len(msgs)-1], "")
}

func genTRPCMessage(reqHead interface{}, serviceName, topic string, rpcMeta bool) (context.Context, codec.Msg) {
	ctx, msg := codec.WithNewMessage(context.Background())
	msg.WithServerReqHead(reqHead)
	msg.WithCompressType(codec.CompressTypeNoop)
	msg.WithCallerServiceName("rpc.kafka.noserver.noservice")
	msg.WithCallerMethod(topic)
	msg.WithCalleeServiceName(serviceName)
	msg.WithCalleeApp("kafka")
	msg.WithServerRPCName("/rpc.kafka.consumer.service/handle")
	msg.WithCalleeMethod(topic)
	if rpcMeta {
		m := getMessageHead(reqHead)
		setTRPCMeta(ctx, m)
	}
	return ctx, msg
}

func setTRPCMeta(ctx context.Context, hs []*sarama.RecordHeader) {
	if hs == nil {
		return
	}
	for _, header := range hs {
		framework.SetMetaData(ctx, string(header.Key), []string{string(header.Value)})
	}
}

func getMessageHead(reqHead interface{}) []*sarama.RecordHeader {
	switch reqHead := reqHead.(type) {
	case *sarama.ConsumerMessage:
		if reqHead != nil {
			return reqHead.Headers
		}
	case []*sarama.ConsumerMessage:

		if len(reqHead) > 0 && reqHead[0] != nil {
			return reqHead[0].Headers
		}
	}

	return nil
}
