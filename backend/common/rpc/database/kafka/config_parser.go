package kafka

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/IBM/sarama"
)

type configParseFunc func(*UserConfig, string) error

func newConfigParsers() map[string]configParseFunc {
	m := make(map[string]configParseFunc)
	parserBasicConfig(m)
	parserAdvanceConfig(m)
	parserBatchConfig(m)
	parserMetadataConfig(m)
	parserDiscoverConfig(m)
	return m
}

func parserBasicConfig(m map[string]configParseFunc) {
	m["clientid"] = func(config *UserConfig, val string) error {
		config.ClientID = val
		return nil
	}
	m["topics"] = func(config *UserConfig, val string) error {
		config.Topics = strings.Split(val, ",")
		return nil
	}
	m["topic"] = func(config *UserConfig, val string) error {
		config.Topic = val
		return nil
	}
	m["group"] = func(config *UserConfig, val string) error {
		config.Group = val
		return nil
	}
	m["async"] = func(config *UserConfig, val string) error {
		var err error
		config.Async, err = parseAsync(val)
		return err
	}
	m["compression"] = func(config *UserConfig, val string) error {
		var err error
		config.Compression, err = parseCompression(val)
		return err
	}
	m["version"] = func(config *UserConfig, val string) error {
		var err error
		config.Version, err = sarama.ParseKafkaVersion(val)
		return err
	}
	m["strategy"] = func(config *UserConfig, val string) error {
		var err error
		config.Strategy, err = parseStrategy(val)
		return err
	}
	m["partitioner"] = func(config *UserConfig, val string) error {
		var err error
		config.Partitioner, err = parsePartitioner(val)
		return err
	}
	m["fetchDefault"] = func(config *UserConfig, val string) error {
		var err error
		config.FetchDefault, err = strconv.Atoi(val)
		return err
	}
	m["fetchMax"] = func(config *UserConfig, val string) error {
		var err error
		config.FetchMax, err = strconv.Atoi(val)
		return err
	}
}

func parserAdvanceConfig(m map[string]configParseFunc) {
	m["maxMessageBytes"] = func(config *UserConfig, val string) error {
		var err error
		config.MaxMessageBytes, err = strconv.Atoi(val)
		return err
	}
	m["flushMessages"] = func(config *UserConfig, val string) error {
		var err error
		config.FlushMessages, err = strconv.Atoi(val)
		return err
	}
	m["flushMaxMessages"] = func(config *UserConfig, val string) error {
		var err error
		config.FlushMaxMessages, err = strconv.Atoi(val)
		return err
	}
	m["flushBytes"] = func(config *UserConfig, val string) error {
		var err error
		config.FlushBytes, err = strconv.Atoi(val)
		return err
	}
	m["flushFrequency"] = func(config *UserConfig, val string) error {
		var err error
		config.FlushFrequency, err = parseDuration(val)
		return err
	}
	m["initial"] = func(config *UserConfig, val string) error {
		var err error
		config.Initial, err = parseInital(val)
		return err
	}
	m["maxWaitTime"] = func(config *UserConfig, val string) error {
		var err error
		config.MaxWaitTime, err = parseDuration(val)
		return err
	}
	m["batch"] = func(config *UserConfig, val string) error {
		var err error
		config.BatchConsumeCount, err = strconv.Atoi(val)
		return err
	}
	m["batchFlush"] = func(config *UserConfig, val string) error {
		var err error
		config.BatchFlush, err = parseDuration(val)
		return err
	}
	m["requiredAcks"] = func(config *UserConfig, val string) error {
		var err error
		config.RequiredAcks, err = parseRequireAcks(val)
		return err
	}
	m["maxRetry"] = func(config *UserConfig, val string) error {
		var err error
		config.MaxRetry, err = strconv.Atoi(val)
		config.ProducerRetry.Max = config.MaxRetry
		return err
	}
	m["rpcMeta"] = func(config *UserConfig, val string) error {
		var err error
		config.TrpcMeta, err = strconv.ParseBool(val)
		return err
	}
	m["idempotent"] = func(config *UserConfig, val string) error {
		var err error
		config.Idempotent, err = strconv.ParseBool(val)
		return err
	}
}

func parserBatchConfig(m map[string]configParseFunc) {
	m["netMaxOpenRequests"] = func(config *UserConfig, val string) error {
		var err error
		config.NetMaxOpenRequests, err = strconv.Atoi(val)
		return err
	}
	m["maxProcessingTime"] = func(config *UserConfig, val string) error {
		var err error
		config.MaxProcessingTime, err = parseDuration(val)
		return err
	}
	m["netDailTimeout"] = func(config *UserConfig, val string) error {
		var err error
		config.NetDailTimeout, err = parseDuration(val)
		return err
	}
	m["netReadTimeout"] = func(config *UserConfig, val string) error {
		var err error
		config.NetReadTimeout, err = parseDuration(val)
		return err
	}
	m["netWriteTimeout"] = func(config *UserConfig, val string) error {
		var err error
		config.NetWriteTimeout, err = parseDuration(val)
		return err
	}
	m["groupSessionTimeout"] = func(config *UserConfig, val string) error {
		var err error
		config.GroupSessionTimeout, err = parseDuration(val)
		return err
	}
	m["groupRebalanceTimeout"] = func(config *UserConfig, val string) error {
		var err error
		config.GroupRebalanceTimeout, err = parseDuration(val)
		return err
	}
	m["groupRebalanceRetryMax"] = func(config *UserConfig, val string) error {
		var err error
		config.GroupRebalanceRetryMax, err = strconv.Atoi(val)
		return err
	}
	m["isolationLevel"] = func(config *UserConfig, val string) error {
		var err error
		config.IsolationLevel, err = parseIsolationLevel(val)
		return err
	}
	m["retryInterval"] = func(config *UserConfig, val string) error {
		var err error
		config.RetryInterval, err = parseRetryInterval(val)
		config.ProducerRetry.RetryInterval = config.RetryInterval
		return err
	}
}

func parserMetadataConfig(m map[string]configParseFunc) {
	m["metadataRetryMax"] = func(config *UserConfig, s string) error {
		metadataRetryMax, err := strconv.Atoi(s)
		if err != nil {
			return err
		}
		if metadataRetryMax < 0 {
			return errors.New("param not support: metadataRetryMax expect a value of no less than 0")
		}
		config.MetadataRetryMax = metadataRetryMax
		return nil
	}
	m["metadataRetryBackoff"] = func(config *UserConfig, s string) error {
		metadataRetryBackoff, err := strconv.Atoi(s)
		if err != nil {
			return err
		}
		if metadataRetryBackoff < 0 {
			return errors.New("param not support: metadataRetryBackoff expect a value of no less than 0")
		}
		config.MetadataRetryBackoff = time.Duration(metadataRetryBackoff) * time.Millisecond
		return nil
	}
	m["metadataRefreshFrequency"] = func(config *UserConfig, s string) error {
		metadataRefreshFrequency, err := strconv.Atoi(s)
		if err != nil {
			return err
		}
		if metadataRefreshFrequency < 0 {
			return errors.New("param not support: metadataRefreshFrequency expect a value of no less than 0")
		}
		config.MetadataRefreshFrequency = time.Duration(metadataRefreshFrequency) * time.Second
		return nil
	}
	m["metadataFull"] = func(config *UserConfig, s string) error {
		var err error
		config.MetadataFull, err = strconv.ParseBool(s)
		return err
	}
	m["metadataAllowAutoTopicCreation"] = func(config *UserConfig, s string) error {
		var err error
		config.MetadataAllowAutoTopicCreation, err = strconv.ParseBool(s)
		return err
	}
}

func parserDiscoverConfig(m map[string]configParseFunc) {
	m["discover"] = func(config *UserConfig, discover string) error {
		config.discover = discover
		return nil
	}
	m["namespace"] = func(config *UserConfig, namespace string) error {
		config.namespace = namespace
		return nil
	}
	m["limiterRate"] = func(config *UserConfig, rate string) error {
		limiterRate, err := strconv.ParseFloat(rate, 64)
		if err != nil {
			return err
		}
		if limiterRate < 0 {
			return errors.New("param not support:limiterRate expect a value of no less than 0")
		}
		if config.RateLimitConfig == nil {
			config.RateLimitConfig = &RateLimitConfig{}
		}
		config.RateLimitConfig.Rate = limiterRate
		return nil
	}
	m["limiterBurst"] = func(config *UserConfig, burst string) error {
		limiterBurst, err := strconv.Atoi(burst)
		if err != nil {
			return err
		}
		if limiterBurst < 0 {
			return errors.New("param not support:limiterBurst expect a value of no less than 0")
		}
		if config.RateLimitConfig == nil {
			config.RateLimitConfig = &RateLimitConfig{}
		}
		config.RateLimitConfig.Burst = limiterBurst
		return nil
	}
}

func parseAsync(val string) (int, error) {
	if val == "1" {
		return 1, nil
	}
	return 0, nil
}

func parseCompression(val string) (sarama.CompressionCodec, error) {
	switch val {
	case "none":
		return sarama.CompressionNone, nil
	case "gzip":
		return sarama.CompressionGZIP, nil
	case "snappy":
		return sarama.CompressionSnappy, nil
	case "lz4":
		return sarama.CompressionLZ4, nil
	case "zstd":
		return sarama.CompressionZSTD, nil
	default:
		return sarama.CompressionNone, errors.New("param not support")
	}
}

func parseStrategy(val string) (sarama.BalanceStrategy, error) {
	switch val {
	case "sticky":
		return sarama.BalanceStrategySticky, nil
	case "range":
		return sarama.BalanceStrategyRange, nil
	case "roundrobin":
		return sarama.BalanceStrategyRoundRobin, nil
	default:
		return nil, errors.New("param not support")
	}
}

func parsePartitioner(val string) (func(topic string) sarama.Partitioner, error) {
	switch val {
	case "random":
		return sarama.NewRandomPartitioner, nil
	case "roundrobin":
		return sarama.NewRoundRobinPartitioner, nil
	case "hash":
		return sarama.NewHashPartitioner, nil
	default:
		return nil, errors.New("param not support")
	}
}

func parseInital(val string) (int64, error) {
	switch val {
	case "newest":
		return sarama.OffsetNewest, nil
	case "oldest":
		return sarama.OffsetOldest, nil
	default:
		return 0, errors.New("param not support")
	}
}

func parseDuration(val string) (time.Duration, error) {
	maxWaitTime, err := strconv.Atoi(val)
	if err != nil {
		return 0, err
	}
	return time.Duration(maxWaitTime) * time.Millisecond, err
}

func parseRequireAcks(val string) (sarama.RequiredAcks, error) {
	ack, err := strconv.Atoi(val)
	if err != nil {
		return 0, err
	}
	saramaAcks := sarama.RequiredAcks(ack)
	if saramaAcks != sarama.WaitForAll && saramaAcks != sarama.WaitForLocal && saramaAcks != sarama.NoResponse {
		return 0, fmt.Errorf("invalid requiredAcks: %s", val)
	}
	return saramaAcks, err
}

func parseIsolationLevel(val string) (sarama.IsolationLevel, error) {
	switch val {
	case "ReadCommitted":
		return sarama.ReadCommitted, nil
	case "ReadUncommitted":
		return sarama.ReadUncommitted, nil
	default:
		return 0, errors.New("param not support")
	}
}

func parseRetryInterval(val string) (time.Duration, error) {
	timeInterval, err := strconv.Atoi(val)
	if err != nil {
		return 0, err
	}
	return time.Duration(timeInterval) * time.Millisecond, nil
}
