package kafka

import (
	"context"

	"github.com/IBM/sarama"
	"google.golang.org/grpc/codes"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/server"
)

type KafkaConsumer interface {
	Handle(ctx context.Context, msg *sarama.ConsumerMessage) error
}

type kafkaHandler func(ctx context.Context, msg *sarama.ConsumerMessage) error

func (h kafkaHandler) Handle(ctx context.Context, msg *sarama.ConsumerMessage) error {
	return h(ctx, msg)
}

var ConsumerServiceDesc = server.ServiceDesc{
	ServiceName: "rpc.kafka.consumer.service",
	HandlerType: ((*KafkaConsumer)(nil)),
	Methods: []server.Method{{
		Name: "/rpc.kafka.consumer.service/handle",
		Func: ConsumerHandle,
	}},
}

func ConsumerHandle(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	filters, err := f(nil)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, _ interface{}) (interface{}, error) {
		msg := codec.Message(ctx)
		m, ok := msg.ServerReqHead().(*sarama.ConsumerMessage)
		if !ok {
			return nil, errs.NewFrameError(codes.DataLoss, "kafka consumer handler: message type invalid")
		}
		return nil, svr.(KafkaConsumer).Handle(ctx, m)
	}
	return filters.Filter(ctx, nil, handleFunc)
}

func RegisterKafkaConsumerService(s server.Service, svr KafkaConsumer) {
	_ = s.Register(&ConsumerServiceDesc, svr)
}

func RegisterKafkaHandlerService(s server.Service,
	handle func(ctx context.Context, msg *sarama.ConsumerMessage) error,
) {
	_ = s.Register(&ConsumerServiceDesc, kafkaHandler(handle))
}

type BatchConsumer interface {
	Handle(ctx context.Context, msgArray []*sarama.ConsumerMessage) error
}

type batchHandler func(ctx context.Context, msgArray []*sarama.ConsumerMessage) error

func (h batchHandler) Handle(ctx context.Context, msgArray []*sarama.ConsumerMessage) error {
	return h(ctx, msgArray)
}

var BatchConsumerServiceDesc = server.ServiceDesc{
	ServiceName: "rpc.kafka.consumer.service",
	HandlerType: ((*BatchConsumer)(nil)),
	Methods: []server.Method{
		{
			Name: "/rpc.kafka.consumer.service/handle",
			Func: BatchConsumerHandle,
		},
	},
}

func BatchConsumerHandle(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	filters, err := f(nil)
	if err != nil {
		return nil, err
	}

	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		msg := codec.Message(ctx)
		msgs, ok := msg.ServerReqHead().([]*sarama.ConsumerMessage)
		if !ok {
			return nil, errs.NewFrameError(codes.DataLoss, "kafka consumer handler: message type invalid")
		}
		return nil, svr.(BatchConsumer).Handle(ctx, msgs)
	}

	return filters.Filter(ctx, nil, handleFunc)
}

func RegisterBatchHandlerService(
	s server.Service,
	handle func(ctx context.Context, msgArray []*sarama.ConsumerMessage) error,
) {
	_ = s.Register(&BatchConsumerServiceDesc, batchHandler(handle))
}
