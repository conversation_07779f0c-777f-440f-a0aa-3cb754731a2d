package kafka

import (
	"context"

	"github.com/IBM/sarama"
	"golang.org/x/time/rate"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport"
)

var newConsumerGroup = sarama.NewConsumerGroup

func init() {
	transport.RegisterServerTransport("kafka", defaultServerTransport)
}

var defaultServerTransport = NewServerTransport()

func NewServerTransport(opt ...transport.ServerTransportOption) *ServerTransport {
	kafkaOpts := &transport.ServerTransportOptions{}
	for _, o := range opt {
		o(kafkaOpts)
	}
	return &ServerTransport{opts: kafkaOpts}
}

type ServerTransport struct {
	opts          *transport.ServerTransportOptions
	tokenProvider sarama.AccessTokenProvider
}

func (s *ServerTransport) ListenAndServe(ctx context.Context, opts ...transport.ListenServeOption) (err error) {
	lsOpts := &transport.ListenServeOptions{}
	for _, opt := range opts {
		opt(lsOpts)
	}

	kafkaUserConfig, err := ParseAddress(lsOpts.Address)
	if err != nil {
		return err
	}

	config := kafkaUserConfig.getServerConfig()
	config = addCredentialConfig(config, s.tokenProvider)

	consumerGroup, err := newConsumerGroup(kafkaUserConfig.Brokers, kafkaUserConfig.Group, config)
	if err != nil {
		return err
	}
	limiter := newLimiter(kafkaUserConfig.RateLimitConfig)

	var handler sarama.ConsumerGroupHandler
	if kafkaUserConfig.BatchConsumeCount > 0 {
		handler = &batchConsumerHandler{
			opts:          lsOpts,
			ctx:           ctx,
			maxNum:        kafkaUserConfig.BatchConsumeCount,
			flushInterval: kafkaUserConfig.BatchFlush,
			retryMax:      kafkaUserConfig.MaxRetry,
			retryInterval: kafkaUserConfig.RetryInterval,
			rpcMeta:       kafkaUserConfig.TrpcMeta,
			limiter:       limiter,
		}
	} else {
		handler = &singleConsumerHandler{
			opts:          lsOpts,
			ctx:           ctx,
			retryMax:      kafkaUserConfig.MaxRetry,
			retryInterval: kafkaUserConfig.RetryInterval,
			rpcMeta:       kafkaUserConfig.TrpcMeta,
			limiter:       limiter,
		}
	}

	go func() {

		defer func() {
			if err := consumerGroup.Close(); err != nil {
				log.Errorf("kafka consumerGroup close return err: %s", err)
			}
		}()

		for {

			if err := consumerGroup.Consume(ctx, kafkaUserConfig.Topics, handler); err != nil {
				log.ErrorContextf(ctx, "kafka server transport: Consume get error:%v", err)
			}

			select {
			case <-ctx.Done():
				log.ErrorContextf(ctx, "kafka server transport: context done:%v, close", ctx.Err())
				return
			default:
			}
		}
	}()
	return nil
}

func newLimiter(conf *RateLimitConfig) *rate.Limiter {
	if conf == nil {
		return rate.NewLimiter(rate.Inf, 0)
	}
	return rate.NewLimiter(rate.Limit(conf.Rate), conf.Burst)
}
