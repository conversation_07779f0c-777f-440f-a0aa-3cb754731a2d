package kafka

import (
	"fmt"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/IBM/sarama"
)

const DefaultClientID = "moego"

type UserConfig struct {
	Brokers         []string
	Topics          []string
	Topic           string
	Group           string
	Async           int
	ClientID        string
	Compression     sarama.CompressionCodec
	Version         sarama.KafkaVersion
	Strategy        sarama.BalanceStrategy
	Partitioner     func(topic string) sarama.Partitioner
	Initial         int64
	FetchDefault    int
	FetchMax        int
	MaxWaitTime     time.Duration
	RequiredAcks    sarama.RequiredAcks
	ReturnSuccesses bool
	Timeout         time.Duration

	MaxMessageBytes   int
	FlushMessages     int
	FlushMaxMessages  int
	FlushBytes        int
	FlushFrequency    time.Duration
	BatchConsumeCount int
	BatchFlush        time.Duration

	MaxRetry                       int
	NetMaxOpenRequests             int
	MaxProcessingTime              time.Duration
	NetDailTimeout                 time.Duration
	NetReadTimeout                 time.Duration
	NetWriteTimeout                time.Duration
	GroupSessionTimeout            time.Duration
	GroupRebalanceTimeout          time.Duration
	GroupRebalanceRetryMax         int
	MetadataRetryMax               int
	MetadataRetryBackoff           time.Duration
	MetadataRefreshFrequency       time.Duration
	MetadataFull                   bool
	MetadataAllowAutoTopicCreation bool
	IsolationLevel                 sarama.IsolationLevel
	RetryInterval                  time.Duration
	ProducerRetry                  struct {
		Max           int
		RetryInterval time.Duration
	}
	TrpcMeta   bool
	Idempotent bool

	discover        string
	namespace       string
	RateLimitConfig *RateLimitConfig
}

type RateLimitConfig struct {
	Rate  float64
	Burst int
}

func (uc *UserConfig) getServerConfig() *sarama.Config {
	sc := sarama.NewConfig()
	sc.Version = uc.Version
	if uc.ClientID == DefaultClientID {
		sc.ClientID = uc.Group
	} else {
		sc.ClientID = uc.ClientID
	}

	sc.Metadata.Retry.Max = uc.MetadataRetryMax
	sc.Metadata.Retry.Backoff = uc.MetadataRetryBackoff
	sc.Metadata.RefreshFrequency = uc.MetadataRefreshFrequency
	sc.Metadata.Full = uc.MetadataFull
	sc.Metadata.AllowAutoTopicCreation = uc.MetadataAllowAutoTopicCreation

	sc.Net.MaxOpenRequests = uc.NetMaxOpenRequests
	sc.Net.DialTimeout = uc.NetDailTimeout
	sc.Net.ReadTimeout = uc.NetReadTimeout
	sc.Net.WriteTimeout = uc.NetWriteTimeout

	sc.Consumer.MaxProcessingTime = uc.MaxProcessingTime
	sc.Consumer.Fetch.Default = int32(uc.FetchDefault)
	sc.Consumer.Fetch.Max = int32(uc.FetchMax)
	sc.Consumer.Offsets.Initial = uc.Initial
	sc.Consumer.Offsets.AutoCommit.Interval = 3 * time.Second
	sc.Consumer.Group.Rebalance.GroupStrategies = []sarama.BalanceStrategy{uc.Strategy}
	sc.Consumer.Group.Rebalance.Timeout = uc.GroupRebalanceTimeout
	sc.Consumer.Group.Rebalance.Retry.Max = uc.GroupRebalanceRetryMax
	sc.Consumer.Group.Session.Timeout = uc.GroupSessionTimeout
	sc.Consumer.MaxWaitTime = uc.MaxWaitTime
	sc.Consumer.IsolationLevel = uc.IsolationLevel
	return sc
}

func GetDefaultConfig() *UserConfig {
	userConfig := &UserConfig{
		Brokers:     nil,
		Topics:      nil,
		Topic:       "",
		Group:       "",
		Async:       0,
		ClientID:    DefaultClientID,
		Compression: sarama.CompressionGZIP,
		Version:     sarama.V2_1_0_0,
		Strategy:    sarama.BalanceStrategySticky,
		Partitioner: sarama.NewRandomPartitioner,
		Initial:     sarama.OffsetNewest,

		FetchDefault: 524288,

		FetchMax: 1048576,

		MaxWaitTime:            time.Second,
		RequiredAcks:           sarama.WaitForAll,
		ReturnSuccesses:        true,
		Timeout:                time.Second,
		MaxMessageBytes:        131072,
		FlushMessages:          0,
		FlushMaxMessages:       0,
		FlushBytes:             0,
		FlushFrequency:         0,
		BatchConsumeCount:      0,
		BatchFlush:             2 * time.Second,
		MaxRetry:               0,
		NetMaxOpenRequests:     5,
		MaxProcessingTime:      100 * time.Millisecond,
		NetDailTimeout:         30 * time.Second,
		NetReadTimeout:         30 * time.Second,
		NetWriteTimeout:        30 * time.Second,
		GroupSessionTimeout:    10 * time.Second,
		GroupRebalanceTimeout:  60 * time.Second,
		GroupRebalanceRetryMax: 4,
		MetadataRetryMax:       1,
		MetadataRetryBackoff:   1000 * time.Millisecond,

		MetadataRefreshFrequency:       120 * time.Second,
		MetadataFull:                   false,
		MetadataAllowAutoTopicCreation: true,
		IsolationLevel:                 0,

		RetryInterval: 3000 * time.Millisecond,

		ProducerRetry: struct {
			Max           int
			RetryInterval time.Duration
		}{Max: 3, RetryInterval: 100 * time.Millisecond},
		TrpcMeta: false,
	}
	return userConfig
}

var (
	addrCfg     = make(map[string]*UserConfig)
	addrCfgLock sync.RWMutex
)

func RegisterAddrConfig(address string, cfg *UserConfig) {
	addrCfgLock.Lock()
	defer addrCfgLock.Unlock()
	addrCfg[address] = cfg
}

func ParseAddress(address string) (*UserConfig, error) {
	addrCfgLock.RLock()
	cfg, ok := addrCfg[address]
	addrCfgLock.RUnlock()
	if ok {
		return cfg, nil
	}

	config := GetDefaultConfig()
	tokens := strings.SplitN(address, "?", 2)
	if len(tokens) != 2 {
		return nil, fmt.Errorf("address format invalid: address: %v, tokens: %v", address, tokens)
	}

	config.Brokers = strings.Split(tokens[0], ",")
	values, err := url.ParseQuery(tokens[1])
	if err != nil {
		return nil, fmt.Errorf("address format invalid: brokers: %v, params: %v, err: %w",
			config.Brokers, tokens[1], err)
	}

	configParsers := newConfigParsers()
	for key := range values {
		value := values.Get(key)
		if value == "" {
			return nil, fmt.Errorf("address format invalid, key: %v, value is empty", key)
		}

		f := configParsers[key]
		if f == nil {
			return nil, fmt.Errorf("address format invalid, unknown key: %v", key)
		}

		if err = f(config, value); err != nil {
			return nil, fmt.Errorf("address format invalid, key: %v, value: %v, err:%w", key, value, err)
		}
	}

	if config.discover == "" {
		return config, nil
	}
	if config.namespace == "" {
		return nil, fmt.Errorf("namespace format invalid:namespace cannot be empty when discover is not empty")
	}
	return config, nil
}
