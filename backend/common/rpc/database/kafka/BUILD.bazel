load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "kafka",
    srcs = [
        "client.go",
        "client_transport.go",
        "codec.go",
        "config.go",
        "config_parser.go",
        "kafka.go",
        "plugin.go",
        "server_transport.go",
        "service_desc.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/database/kafka",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/client",
        "//backend/common/rpc/framework/codec",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/common/rpc/framework/plugin",
        "//backend/common/rpc/framework/server",
        "//backend/common/rpc/framework/transport",
        "@com_github_aws_aws_msk_iam_sasl_signer_go//signer",
        "@com_github_aws_aws_sdk_go_v2//aws",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_ibm_sarama//:sarama",
        "@org_golang_google_grpc//codes",
        "@org_golang_x_time//rate",
    ],
)
