package kafka

import (
	"context"
	"fmt"

	"github.com/IBM/sarama"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/client"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
)

type Client interface {
	Produce(ctx context.Context, key, value []byte,
		headers ...sarama.RecordHeader) error
	SendMessage(ctx context.Context, topic string, key, value []byte,
		headers ...sarama.RecordHeader) (partition int32, offset int64, err error)
	AsyncSendMessage(ctx context.Context, topic string, key, value []byte,
		headers ...sarama.RecordHeader) (err error)

	SendSaramaMessage(ctx context.Context, sMsg sarama.ProducerMessage) (partition int32, offset int64, err error)
}

type kafkaCli struct {
	ServiceName string
	Client      client.Client
	opts        []client.Option
}

var NewClientProxy = func(name string, opts ...client.Option) Client {
	c := &kafkaCli{
		ServiceName: name,
		Client:      client.DefaultClient,
	}

	c.opts = make([]client.Option, 0, len(opts)+2)
	c.opts = append(c.opts, client.WithProtocol("kafka"))
	c.opts = append(c.opts, opts...)
	return c
}

type Request struct {
	Topic     string
	Key       []byte
	Value     []byte
	Async     bool
	Partition int32
	Headers   []sarama.RecordHeader
	Message   sarama.ProducerMessage
}

type Response struct {
	Partition int32
	Offset    int64
}

func (c *kafkaCli) Produce(ctx context.Context, key, value []byte, headers ...sarama.RecordHeader) error {
	req := &Request{
		Message: sarama.ProducerMessage{
			Key:   sarama.ByteEncoder(key),
			Value: sarama.ByteEncoder(value),
		},
	}
	if len(headers) > 0 {
		req.Message.Headers = headers
	}

	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName(fmt.Sprintf("/%s/produce", c.ServiceName))
	msg.WithCalleeServiceName(c.ServiceName)
	msg.WithSerializationType(-1)
	msg.WithCompressType(0)
	msg.WithClientReqHead(req)
	rsp, ok := msg.ClientRspHead().(*Response)
	if !ok {
		rsp = &Response{}
		msg.WithClientRspHead(rsp)

	}

	return c.Client.Invoke(ctx, req, rsp, c.opts...)
}

func (c *kafkaCli) SendMessage(ctx context.Context, topic string, key, value []byte,
	headers ...sarama.RecordHeader,
) (partition int32, offset int64, err error) {
	req := &Request{
		Message: sarama.ProducerMessage{
			Topic: topic,
			Key:   sarama.ByteEncoder(key),
			Value: sarama.ByteEncoder(value),
		},
	}
	if len(headers) > 0 {
		req.Message.Headers = headers
	}
	rsp := &Response{}

	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName(fmt.Sprintf("/%s/send", c.ServiceName))
	msg.WithCalleeServiceName(c.ServiceName)
	msg.WithSerializationType(-1)
	msg.WithCompressType(0)
	msg.WithClientReqHead(req)
	msg.WithClientRspHead(rsp)

	err = c.Client.Invoke(ctx, req, rsp, c.opts...)
	if err != nil {
		return 0, 0, err
	}

	return rsp.Partition, rsp.Offset, nil
}

func (c *kafkaCli) AsyncSendMessage(ctx context.Context, topic string, key, value []byte,
	headers ...sarama.RecordHeader,
) (err error) {
	req := &Request{
		Async: true,
		Message: sarama.ProducerMessage{
			Topic: topic,
			Key:   sarama.ByteEncoder(key),
			Value: sarama.ByteEncoder(value),
		},
	}
	if len(headers) > 0 {
		req.Message.Headers = headers
	}
	rsp := &Response{}

	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName(fmt.Sprintf("/%s/asyncSend", c.ServiceName))
	msg.WithCalleeServiceName(c.ServiceName)
	msg.WithSerializationType(-1)
	msg.WithCompressType(0)
	msg.WithClientReqHead(req)
	msg.WithClientRspHead(rsp)

	err = c.Client.Invoke(ctx, req, rsp, c.opts...)
	if err != nil {
		return err
	}

	return nil
}

func (c *kafkaCli) SendSaramaMessage(
	ctx context.Context,
	sMsg sarama.ProducerMessage,
) (partition int32, offset int64, err error) {
	req := &Request{
		Message: sMsg,
	}
	rsp := &Response{}

	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName(fmt.Sprintf("/%s/sendSarama", c.ServiceName))
	msg.WithCalleeServiceName(c.ServiceName)
	msg.WithSerializationType(-1)
	msg.WithCompressType(0)
	msg.WithClientReqHead(req)
	msg.WithClientRspHead(rsp)

	if err := c.Client.Invoke(ctx, req, rsp, c.opts...); err != nil {
		return 0, 0, err
	}

	return rsp.Partition, rsp.Offset, nil
}
