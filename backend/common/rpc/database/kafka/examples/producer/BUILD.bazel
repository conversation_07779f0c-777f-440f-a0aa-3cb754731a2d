load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "producer_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/database/kafka/examples/producer",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/kafka",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/framework",
    ],
)

go_binary(
    name = "producer",
    embed = [":producer_lib"],
    visibility = ["//visibility:public"],
)
