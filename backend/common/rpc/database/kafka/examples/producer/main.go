// Package main is the main package.
// Used to test client production messages
package main

import (
	"fmt"

	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/database/kafka"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework"
)

func main() {
	framework.ServerConfigPath = "/Users/<USER>/moego/moego/backend/common/rpc/database/kafka/examples/producer/config.yaml"
	framework.NewServer()
	key := "key"
	value := "value"
	proxy := kafka.NewClientProxy("moego.kafka.producer")
	err := proxy.Produce(framework.BackgroundContext(), []byte(key), []byte(value))
	if err != nil {
		fmt.Println(err)
	}
}
