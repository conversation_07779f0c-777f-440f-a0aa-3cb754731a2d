// Package main is the main package.
// Used to test server consumption messages
package main

import (
	"context"

	"github.com/IBM/sarama"

	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/database/kafka"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func main() {
	framework.ServerConfigPath = "/Users/<USER>/moego/moego/backend/common/rpc/database/kafka/examples/consumer/config.yaml"
	s := framework.NewServer()

	kafka.RegisterKafkaHandlerService(s.Service("moego.kafka.consumer"), Handle)

	if err := s.Serve(); err != nil {
		panic(err)
	}
}

func Handle(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if rawContext, ok := kafka.GetRawSaramaContext(ctx); ok {
		log.Infof("InitialOffset:%d", rawContext.Claim.InitialOffset())
	}
	log.Infof("get kafka message: %+v", msg)
	return nil
}
