load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "consumer_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/database/kafka/examples/consumer",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/kafka",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "@com_github_ibm_sarama//:sarama",
    ],
)

go_binary(
    name = "consumer",
    embed = [":consumer_lib"],
    visibility = ["//visibility:public"],
)
