// Package main is the main package.
// Used to test server consumption of multiple messages
package main

import (
	"context"
	"fmt"

	"github.com/IBM/sarama"

	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/database/kafka"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func main() {
	framework.ServerConfigPath = "/Users/<USER>/moego/moego/backend/common/rpc/database/kafka/examples/batchconsumer/config.yaml"
	s := framework.NewServer()

	kafka.RegisterBatchHandlerService(s.Service("moego.kafka.consumer"), Handle)

	go Produce()

	if err := s.Serve(); err != nil {
		panic(err)
	}
}

func Produce() {
	ctx := context.TODO()
	for i := 0; i < 17; i++ {
		key := fmt.Sprintf("key_%d", i)
		value := fmt.Sprintf("value_%d", i)
		proxy := kafka.NewClientProxy("rpc.kafka.producer.service")
		p, offset, err := proxy.SendMessage(ctx, "test", []byte(key), []byte(value))
		if err != nil {
			log.ErrorContextf(ctx, "send msg failed, %s", err)
			return
		}
		log.Infof("[produce][partition]%v\t[offset]%v\t[key]%v\t[value]%v\n",
			p, offset, key, value)
	}
}

func Handle(ctx context.Context, msgArray []*sarama.ConsumerMessage) error {
	log.Infof("len(msgArray) = %d", len(msgArray))
	for _, v := range msgArray {
		log.Infof("[consume][topic]%v\t[partition]%v\t[offset]%v\t[key]%v\t[value]%v\n",
			v.Topic, v.Partition, v.Offset, string(v.Key), string(v.Value))
	}
	return nil
}
