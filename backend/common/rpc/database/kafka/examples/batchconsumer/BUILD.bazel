load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "batchconsumer_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/database/kafka/examples/batchconsumer",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/kafka",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "@com_github_ibm_sarama//:sarama",
    ],
)

go_binary(
    name = "batchconsumer",
    embed = [":batchconsumer_lib"],
    visibility = ["//visibility:public"],
)
