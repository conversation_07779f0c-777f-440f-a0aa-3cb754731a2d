package kafka

import (
	"context"
	"crypto/tls"
	"sync"
	"time"

	"github.com/IBM/sarama"
	"github.com/aws/aws-msk-iam-sasl-signer-go/signer"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/bytedance/sonic"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/plugin"
)

const (
	pluginType = "database"
	pluginName = "kafka"
)

func init() {
	plugin.Register(pluginName, &Plugin{})
}

type Config struct {
	MaxRequestSize  int32       `yaml:"max_request_size"`
	MaxResponseSize int32       `yaml:"max_response_size"`
	RewriteLog      bool        `yaml:"rewrite_log"`
	Credential      *Credential `yaml:"credential"`
}

type Plugin struct{}

func (k *Plugin) Type() string {
	return pluginType
}

func (k *Plugin) Setup(name string, configDesc plugin.Decoder) error {
	var config Config
	if err := configDesc.Decode(&config); err != nil {
		return err
	}
	configStr, _ := sonic.MarshalString(config)
	log.Infof("kafka Plugin Credential config:%s", configStr)

	if config.MaxRequestSize > 0 {
		sarama.MaxRequestSize = config.MaxRequestSize
	}
	if config.MaxResponseSize > 0 {
		sarama.MaxResponseSize = config.MaxResponseSize
	}
	if config.RewriteLog {
		sarama.Logger = LogReWriter{}
	}
	if config.Credential != nil {
		defaultServerTransport.tokenProvider = newTokenProvider(config.Credential)
		defaultClientTransport.tokenProvider = newTokenProvider(config.Credential)
	}

	return nil
}

type LogReWriter struct{}

func (LogReWriter) Print(v ...interface{}) {
	log.Info(v...)
}

func (LogReWriter) Printf(format string, v ...interface{}) {
	log.Infof(format, v...)
}

func (LogReWriter) Println(v ...interface{}) {
	log.Info(v...)
}

type Credential struct {
	// AWS Region
	Region string `yaml:"region"`
	// AWS Access key ID
	AccessKeyID string `yaml:"access_key_id"`
	// AWS Secret Access Key
	SecretAccessKey string `yaml:"secret_access_key"`
}

type MSKAccessTokenProvider struct {
	region         string
	provider       aws.CredentialsProvider
	mu             sync.Mutex
	cachedToken    *sarama.AccessToken
	cachedTokenExp time.Time
	refreshBuffer  time.Duration
}

func newTokenProvider(credential *Credential) *MSKAccessTokenProvider {
	return &MSKAccessTokenProvider{
		region: credential.Region,
		provider: &MSKCredentialsProvider{
			accessKeyID:     credential.AccessKeyID,
			secretAccessKey: credential.SecretAccessKey,
		},
		refreshBuffer: 300 * time.Second,
	}
}

func (m *MSKAccessTokenProvider) Token() (*sarama.AccessToken, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	if m.cachedToken != nil && time.Now().Before(m.cachedTokenExp.Add(-m.refreshBuffer)) {
		return m.cachedToken, nil
	}
	// aws oauth token 默认过期时间为 900s
	token, expiryTime, err := signer.GenerateAuthTokenFromCredentialsProvider(context.TODO(), m.region, m.provider)
	if err != nil {
		m.cachedToken = nil
		m.cachedTokenExp = time.Time{}
		log.Errorf("Failed to generate msk access token: %v", err)
		return nil, err
	}
	m.cachedToken = &sarama.AccessToken{Token: token}
	m.cachedTokenExp = time.UnixMilli(expiryTime)
	return m.cachedToken, nil
}

type MSKCredentialsProvider struct {
	accessKeyID     string
	secretAccessKey string
}

func (m *MSKCredentialsProvider) Retrieve(_ context.Context) (aws.Credentials, error) {
	return aws.Credentials{
		AccessKeyID:     m.accessKeyID,
		SecretAccessKey: m.secretAccessKey,
		Source:          "EventBusCredentials",
		CanExpire:       false,
	}, nil
}

func addCredentialConfig(c *sarama.Config, tokenProvider sarama.AccessTokenProvider) *sarama.Config {
	if tokenProvider == nil {
		return c
	}
	c.Net.SASL.Enable = true
	c.Net.SASL.Mechanism = sarama.SASLTypeOAuth
	c.Net.SASL.TokenProvider = tokenProvider
	c.Net.TLS.Enable = true
	c.Net.TLS.Config = &tls.Config{}
	return c
}
