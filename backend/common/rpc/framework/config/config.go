package config

import "sync"

type Config interface {
	Load() error
	Reload()
	Get(string, interface{}) interface{}
	Unmarshal(interface{}) error
}

type DataProvider interface {
	// Name returns the name of the data provider
	Name() string
	// Read reads the specified file and returns the content
	Read(string) ([]byte, error)
}

type Codec interface {
	Name() string
	Unmarshal([]byte, interface{}) error
}

var providerMap = make(map[string]DataProvider)

func RegisterProvider(provider DataProvider) {
	providerMap[provider.Name()] = provider
}

func GetProvider(name string) DataProvider {
	return providerMap[name]
}

var (
	codecMap = make(map[string]Codec)
	lock     sync.RWMutex
)

func RegisterCodec(codec Codec) {
	lock.Lock()
	defer lock.Unlock()
	codecMap[codec.Name()] = codec
}

func GetCodec(name string) Codec {
	lock.RLock()
	defer lock.RUnlock()
	return codecMap[name]
}

func Load(path string, opts ...LoadOption) (Config, error) {
	return DefaultConfigLoader.Load(path, opts...)
}
