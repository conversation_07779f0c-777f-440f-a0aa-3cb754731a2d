package config

func WithCodec(name string) LoadOption {
	return func(c *config) {
		c.decoder = GetCodec(name)
	}
}

func WithProvider(name string) LoadOption {
	return func(c *config) {
		c.p = GetProvider(name)
	}
}

// WithWatch returns an option to start watch model
func WithWatch() LoadOption {
	return func(c *config) {
		c.watch = true
	}
}

// WithWatchHook returns an option to set log func for config change logger
func WithWatchHook(f func(msg WatchMessage)) LoadOption {
	return func(c *config) {
		c.watchHook = f
	}
}

// options is config option.
type options struct{}

// Option is the option for config provider sdk.
type Option func(*options)
