package config

import (
	"os"
	"sync"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func init() {
	RegisterProvider(newFileProvider())
}

func newFileProvider() *FileProvider {
	fp := &FileProvider{
		cache:   make(map[string]string),
		modTime: make(map[string]int64),
	}
	return fp
}

type FileProvider struct {
	cache   map[string]string
	modTime map[string]int64
	mu      sync.RWMutex
}

func (*FileProvider) Name() string {
	return "file"
}

func (fp *FileProvider) Read(path string) ([]byte, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		log.Tracef("Failed to read file %v", err)
		return nil, err
	}
	return data, nil
}
