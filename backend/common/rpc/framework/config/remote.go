package config

import (
	"context"
	"errors"
)

var (
	configMap = make(map[string]KVConfig)
)

// ErrConfigNotSupport is not supported config error
var ErrConfigNotSupport = errors.New("config: not support")

// KVConfig defines a kv config interface.
type KVConfig interface {
	KV
	Watcher
	Name() string
}

// Register registers a kv config by its name.
func Register(c KVConfig) {
	lock.Lock()
	configMap[c.Name()] = c
	lock.Unlock()
}

// Get returns a kv config by name.
func Get(name string) KVConfig {
	lock.RLock()
	c := configMap[name]
	lock.RUnlock()
	return c
}

// GlobalKV returns an instance of kv config center.
func GlobalKV() KV {
	return globalKV
}

// SetGlobalKV sets the instance of kv config center.
func SetGlobalKV(kv KV) {
	globalKV = kv
}

// KV defines a kv storage for config center.
type KV interface {
	// Put puts or updates config value by key.
	Put(ctx context.Context, key, val string, opts ...Option) error

	// Get returns config value by key.
	Get(ctx context.Context, key string, opts ...Option) (Response, error)

	// Del deletes config value by key.
	Del(ctx context.Context, key string, opts ...Option) error
}

// Watcher defines the interface of config center watch event.
type Watcher interface {
	// Watch watches the config key change event.
	Watch(ctx context.Context, key string, opts ...Option) (<-chan Response, error)
}

var globalKV KV = &noopKV{}

// noopKV is an empty implementation of KV interface.
type noopKV struct{}

// Put does nothing but returns nil.
func (kv *noopKV) Put(ctx context.Context, key, val string, opts ...Option) error {
	return nil
}

// Get returns not supported error.
func (kv *noopKV) Get(ctx context.Context, key string, opts ...Option) (Response, error) {
	return nil, ErrConfigNotSupport
}

// Del does nothing but returns nil.
func (kv *noopKV) Del(ctx context.Context, key string, opts ...Option) error {
	return nil
}

// EventType defines the event type of config change.
type EventType uint8

const (
	// EventTypeNull represents null event.
	EventTypeNull EventType = 0

	// EventTypePut represents set or update config event.
	EventTypePut EventType = 1

	// EventTypeDel represents delete config event.
	EventTypeDel EventType = 2
)

// Response defines config center's response interface.
type Response interface {
	// Value returns config value as string.
	Value() string

	// MetaData returns extra metadata. With option,
	// we can implement some extra features for different config center,
	// such as namespace, group, lease, etc.
	MetaData() map[string]string

	// Event returns the type of watch event.
	Event() EventType
}
