package config

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"github.com/spf13/cast"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/internal/replacesecret"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/secret"
)

var DefaultConfigLoader = &ConfigLoader{}

type ConfigLoader struct{}

func (loader *ConfigLoader) Load(path string, opts ...LoadOption) (Config, error) {
	c, err := newConfig(path, opts...)
	if err != nil {
		return nil, err
	}
	if err := c.init(); err != nil {
		return nil, err
	}
	return c, nil
}

// defaultNotifyChange default hook for notify config changed
var defaultWatchHook = func(message WatchMessage) {}

// SetDefaultWatchHook set default hook notify when config changed
func SetDefaultWatchHook(f func(message WatchMessage)) {
	defaultWatchHook = f
}

// WatchMessage change message
type WatchMessage struct {
	Provider  string // provider name
	Path      string // config path
	ExpandEnv bool   // expend env status
	Codec     string // codec
	Watch     bool   // status for start watch
	Value     []byte // config content diff ?
	Error     error  // load error message, success is empty string
}

type config struct {
	id      string
	p       DataProvider
	path    string
	decoder Codec

	// because function is not support comparable in singleton, so the following options work only for the first load
	watch     bool
	watchHook func(message WatchMessage)

	mutex sync.RWMutex
	value *entity
}

type entity struct {
	raw  []byte
	data interface{}
}

func newEntity() *entity {
	return &entity{
		data: make(map[string]interface{}),
	}
}

func newConfig(path string, opts ...LoadOption) (*config, error) {
	c := &config{
		path:    path,
		p:       GetProvider("file"),
		decoder: GetCodec("yaml"),
	}
	for _, opt := range opts {
		opt(c)
	}
	if c.p == nil {
		return nil, fmt.Errorf("provider not found")
	}
	if c.decoder == nil {
		return nil, fmt.Errorf("decoder not found")
	}

	const idFmt = "provider:%s path:%s codec:%s"
	c.id = fmt.Sprintf(idFmt, c.p.Name(), c.path, c.decoder.Name())
	return c, nil
}

func (c *config) get() *entity {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	if c.value != nil {
		return c.value
	}
	return newEntity()
}

func (c *config) init() error {
	c.mutex.RLock()
	if c.value != nil {
		c.mutex.RUnlock()
		return nil
	}
	c.mutex.RUnlock()

	c.mutex.Lock()
	defer c.mutex.Unlock()
	if c.value != nil {
		return nil
	}

	data, err := c.p.Read(c.path)
	if err != nil {
		return fmt.Errorf("fail to read: %s, id: %s", err, c.id)
	}
	return c.set(data)
}

func (c *config) set(data []byte) error {
	// load secrets
	var sourceConfig secret.Config
	if err := c.decoder.Unmarshal(data, &sourceConfig); err != nil {
		return err
	}
	secretClient := secret.NewSecretClient()
	if secrets, err := secret.LoadAllSecrets(context.Background(), secretClient, &sourceConfig); err != nil {
		return err
	} else {
		data = replacesecret.ReplaceSecret(data, secrets)
	}

	e := newEntity()
	e.raw = data
	err := c.decoder.Unmarshal(data, &e.data)
	if err != nil {
		return fmt.Errorf("fail to unmarshal: %s, id: %s", err, c.id)
	}
	c.value = e
	return nil
}

func (c *config) Load() error {
	if c.p == nil {
		return fmt.Errorf("provider not found")
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()
	data, err := c.p.Read(c.path)
	if err != nil {
		return fmt.Errorf("fail to read: %s, id: %s", err, c.id)
	}
	return c.set(data)
}

func (c *config) Reload() {
	if err := c.Load(); err != nil {
		log.Tracef("fail to reload %s: %v", c.id, err)
	}
}

func (c *config) Get(key string, defaultValue interface{}) interface{} {
	if v, ok := c.search(key); ok {
		return v
	}
	return defaultValue
}

func (c *config) Unmarshal(out interface{}) error {
	return c.decoder.Unmarshal(c.get().raw, out)
}

func (c *config) Bytes() []byte {
	return c.get().raw
}

func (c *config) search(key string) (interface{}, bool) {
	e := c.get()

	unmarshalledData, ok := e.data.(map[string]interface{})
	if !ok {
		return nil, false
	}

	subkeys := strings.Split(key, ".")
	value, err := search(unmarshalledData, subkeys)
	if err != nil {
		log.Debugf("config: search key %s failed: %v", key, err)
		return value, false
	}
	return value, true
}

func search(unmarshalledData map[string]interface{}, keys []string) (interface{}, error) {
	if len(keys) == 0 {
		return nil, fmt.Errorf("empty key")
	}

	key, ok := unmarshalledData[keys[0]]
	if !ok {
		return nil, fmt.Errorf("key %s not found", keys[0])
	}

	if len(keys) == 1 {
		return key, nil
	}
	switch key := key.(type) {
	case map[interface{}]interface{}:
		return search(cast.ToStringMap(key), keys[1:])
	case map[string]interface{}:
		return search(key, keys[1:])
	default:
		return nil, fmt.Errorf("key %s is not a map", keys[0])
	}
}

type LoadOption func(*config)
