load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "config",
    srcs = [
        "codec.go",
        "config.go",
        "loader.go",
        "options.go",
        "provider.go",
        "remote.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/framework/config",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/internal/replacesecret",
        "//backend/common/rpc/framework/log",
        "//backend/common/rpc/framework/secret",
        "@com_github_spf13_cast//:cast",
        "@in_gopkg_yaml_v3//:yaml_v3",
    ],
)
