load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "secret",
    srcs = ["secret.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/framework/secret",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/log",
        "@com_github_aws_aws_sdk_go_v2//aws",
        "@com_github_aws_aws_sdk_go_v2_config//:config",
        "@com_github_aws_aws_sdk_go_v2_service_secretsmanager//:secretsmanager",
        "@in_gopkg_yaml_v3//:yaml_v3",
        "@org_uber_go_zap//:zap",
    ],
)
