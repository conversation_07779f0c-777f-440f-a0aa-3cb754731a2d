package secret

import (
	"context"
	"encoding/json"
	"fmt"
	"maps"
	"sync"

	"github.com/aws/aws-sdk-go-v2/aws"
	awsconfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"go.uber.org/zap"
	yaml "gopkg.in/yaml.v3"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

var secretClient *secretsmanager.Client

var secretOnce sync.Once

type Source struct {
	Name   string `yaml:"name"`
	Prefix string `yaml:"prefix"`
	Option bool   `yaml:"option"`
}

type Config struct {
	Secrets []Source `yaml:"secrets"`
}

type SecretsManagerClient interface {
	GetSecretValue(ctx context.Context, params *secretsmanager.GetSecretValueInput,
		optFns ...func(*secretsmanager.Options)) (*secretsmanager.GetSecretValueOutput, error)
}

// NewSecretClient returns a SecretsManagerClient
func NewSecretClient() *secretsmanager.Client {
	secretOnce.Do(func() {
		region := "us-west-2"
		cfg, err := awsconfig.LoadDefaultConfig(context.TODO(), awsconfig.WithRegion(region))
		if err != nil {
			log.Fatal(err)
		}
		secretClient = secretsmanager.NewFromConfig(cfg)
	})
	return secretClient
}

func loadSecretWithPrefix(ctx context.Context, client SecretsManagerClient,
	secretName, prefix string) (map[string]string, error) {
	prefixedMap := make(map[string]string)
	input := &secretsmanager.GetSecretValueInput{
		SecretId:     aws.String(secretName),
		VersionStage: aws.String("AWSCURRENT"),
	}

	result, err := client.GetSecretValue(ctx, input)
	if err != nil {
		return prefixedMap, err
	}

	var secretMap = make(map[string]string)

	// Try to unmarshal as JSON first
	err = json.Unmarshal([]byte(*result.SecretString), &secretMap)
	if err != nil {
		// If JSON unmarshalling fails, try YAML
		err = yaml.Unmarshal([]byte(*result.SecretString), &secretMap)
		if err != nil {
			return prefixedMap, err
		}
	}

	for k, v := range secretMap {
		prefixedMap[prefix+k] = v
	}
	// debug
	// 定义一个切片来保存所有的键
	var keys []string
	// 遍历map，收集所有键
	for key := range prefixedMap {
		keys = append(keys, key)
	}
	log.DebugContext(ctx, "prefixedMap", zap.Any("prefixedMap", keys))

	return prefixedMap, nil
}

func LoadAllSecrets(ctx context.Context, client SecretsManagerClient,
	sourceConfig *Config) (map[string]string, error) {
	log.DebugContext(ctx, "start loadAllSecrets")

	var wg sync.WaitGroup
	secretMaps := sync.Map{}
	errors := sync.Map{}

	for _, secret := range sourceConfig.Secrets {
		wg.Add(1)
		go func(s Source) {
			defer wg.Done()
			secretMap, err := loadSecretWithPrefix(ctx, client, s.Name, s.Prefix)
			if err != nil {
				if s.Option {
					log.WarnContext(ctx, "Optional secret not found", zap.String("secretName", s.Name), zap.Error(err))
				} else {
					errors.Store(s.Name, err)
					log.ErrorContext(ctx, "Failed to load secret", zap.String("secretName", s.Name), zap.Error(err))
				}
			}
			secretMaps.Store(s.Name, secretMap)
		}(secret)
	}

	wg.Wait()

	// Collect all errors
	var errorList []error
	errors.Range(func(_, value interface{}) bool {
		errorList = append(errorList, value.(error))
		return true
	})

	if len(errorList) > 0 {
		return nil, fmt.Errorf("errors loading secrets: %v", errorList)
	}

	// Collect all secret maps
	resultMap := make(map[string]string)
	secretMaps.Range(func(_, value interface{}) bool {
		secretMap := value.(map[string]string)
		maps.Copy(resultMap, secretMap)
		return true
	})

	log.DebugContext(ctx, "end loadAllSecrets")

	return resultMap, nil
}
