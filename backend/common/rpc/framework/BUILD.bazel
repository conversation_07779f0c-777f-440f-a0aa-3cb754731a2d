load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "framework",
    srcs = [
        "config.go",
        "rpc.go",
        "utils.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/framework",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/admin",
        "//backend/common/rpc/framework/client",
        "//backend/common/rpc/framework/codec",
        "//backend/common/rpc/framework/filter",
        "//backend/common/rpc/framework/log",
        "//backend/common/rpc/framework/plugin",
        "//backend/common/rpc/framework/secret",
        "//backend/common/rpc/framework/server",
        "//backend/common/rpc/framework/transport",
        "@com_github_moegolibrary_go_lib//zlog",
        "@com_github_spf13_viper//:viper",
        "@in_gopkg_yaml_v3//:yaml_v3",
        "@org_uber_go_automaxprocs//maxprocs",
        "@org_uber_go_zap//:zap",
    ],
)
