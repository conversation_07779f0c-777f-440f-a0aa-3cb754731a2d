package framework

import (
	"context"
	"flag"
	"fmt"
	"net"
	"os"
	"regexp"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	"github.com/spf13/viper"
	"go.uber.org/zap"
	yaml "gopkg.in/yaml.v3"

	"github.com/MoeGolibrary/go-lib/zlog"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/client"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/plugin"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/secret"
)

const (
	defaultConfigPath = "./config/local/config.yaml"
)

var (
	ServerConfigPath = defaultConfigPath
	placeholderRegex = regexp.MustCompile(`\$?\{([^}]+)\}`)
)

func serverConfigPath() string {
	// 执行pwd
	dir, err := os.Getwd()
	if err != nil {
		panic(err)
	}
	fmt.Println("current dir:", dir)
	if ServerConfigPath == defaultConfigPath && !flag.Parsed() {
		flag.StringVar(&ServerConfigPath, "config", defaultConfigPath, "config file path")
		flag.Parse()
	}
	return ServerConfigPath
}

type Config struct {
	Global struct {
		Namespace      string `yaml:"namespace"`
		EnvName        string `yaml:"env_name"`
		LocalIP        string `yaml:"local_ip"`
		ReadBufferSize *int   `yaml:"read_buffer_size"`
	}
	Server struct {
		App    string `yaml:"app"`    // application name
		Server string `yaml:"server"` // server name
		Admin  struct {
			IP           string `yaml:"ip"`
			Nic          string `yaml:"nic"`
			Port         uint16 `yaml:"port"` // Default is 9028.
			ReadTimeout  int    `yaml:"read_timeout"`
			WriteTimeout int    `yaml:"write_timeout"`
		}
		Transport string           `yaml:"transport"` // transport type
		Network   string           `yaml:"network"`   // network type
		Protocol  string           `yaml:"protocol"`  // protocol type
		Filter    []string         `yaml:"filter"`    // filter
		Service   []*ServiceConfig `yaml:"service"`
		Timeout   int              `yaml:"timeout"` // timeout in milliseconds
	}
	Client  ClientConfig  `yaml:"client"`
	Plugins plugin.Config `yaml:"plugins"`
}

type ServiceConfig struct {
	Nic       string   `yaml:"nic"`
	IP        string   `yaml:"ip"`
	Name      string   `yaml:"name"` // service name
	Port      uint16   `yaml:"port"`
	Address   string   `yaml:"address"`
	Network   string   `yaml:"network"`
	Protocol  string   `yaml:"protocol"`
	Timeout   int      `yaml:"timeout"` // longest time in milliseconds for a handler to handle a request
	Filter    []string `yaml:"filter"`
	Transport string   `yaml:"transport"`
}

type ClientConfig struct {
	Network   string                  `yaml:"network"`
	Protocol  string                  `yaml:"protocol"`
	Filter    []string                `yaml:"filter"`
	Namespace string                  `yaml:"namespace"`
	Transport string                  `yaml:"transport"`
	Timeout   int                     `yaml:"timeout"` // timeout in milliseconds
	Service   []*client.BackendConfig `yaml:"service"`
}

func LoadConfig(configPath string) (*Config, error) {
	cfg, err := parseConfig(configPath)
	if err != nil {
		return nil, err
	}
	if err := RepairConfig(cfg); err != nil {
		return nil, err
	}
	return cfg, nil
}

func SetupPlugins(cfg plugin.Config) (func() error, error) {
	if cfg == nil {
		return func() error { return nil }, nil
	}
	return cfg.SetupClosables()
}

func parseConfig(configPath string) (*Config, error) {
	log.Infof("loading config from %s", configPath)

	v := viper.New()
	v.SetConfigFile(configPath)
	if err := v.ReadInConfig(); err != nil {
		return nil, err
	}

	// load secrets
	var sourceConfig secret.Config
	if err := v.Unmarshal(&sourceConfig); err != nil {
		return nil, err
	}
	var (
		secrets map[string]string
		err     error
	)
	if len(sourceConfig.Secrets) > 0 {
		secrets, err = secret.LoadAllSecrets(context.Background(), secret.NewSecretClient(), &sourceConfig)
		if err != nil {
			return nil, err
		}
		replaceSecrets(v, secrets)
	}

	// unmarshal config
	cfg := defaultConfig()
	s, err := yaml.Marshal(v.AllSettings())
	if err != nil {
		return nil, err
	}
	if err := yaml.Unmarshal(s, cfg); err != nil {
		return nil, err
	}
	return cfg, nil
}

func SetupClients(cfg *ClientConfig) error {
	for _, backendCfg := range cfg.Service {
		if err := client.RegisterClientConfig(backendCfg.Callee, backendCfg); err != nil {
			return err
		}
	}
	if _, ok := client.DefaultClientConfig()["*"]; !ok {
		if err := client.RegisterClientConfig("*", &client.BackendConfig{
			Network:   cfg.Network,
			Protocol:  cfg.Protocol,
			Namespace: cfg.Namespace,
			Transport: cfg.Transport,
			Timeout:   cfg.Timeout,
			Filter:    cfg.Filter,
		}); err != nil {
			return err
		}
	}
	return nil
}

func replaceSecrets(cfg *viper.Viper, secrets map[string]string) {
	cfgMap := replacePlaceholders(cfg.AllSettings(), secrets)
	if updatedConfigMap, ok := cfgMap.(map[string]any); ok {
		if err := cfg.MergeConfigMap(updatedConfigMap); err != nil {
			log.Errorf("Failed to merge updated config: %v", err)
		}
	} else {
		log.Error("Updated config is not of type map[string]any")
	}
}

// replacePlaceholders 递归替换占位符
func replacePlaceholders(config interface{}, valueMap map[string]string) interface{} {
	switch v := config.(type) {
	case map[string]interface{}:
		updatedMap := make(map[string]interface{})
		for key, value := range v {
			updatedMap[key] = replacePlaceholders(value, valueMap)
		}
		return updatedMap
	case []interface{}:
		updatedSlice := make([]interface{}, len(v))
		for i, value := range v {
			updatedSlice[i] = replacePlaceholders(value, valueMap)
		}
		return updatedSlice
	case string:
		return replaceVars(v, valueMap)
	default:
		return v
	}
}

// replaceVars 替换变量占位符，支持 ${varName:defaultVal} 格式
func replaceVars(val string, valueMap map[string]string) string {
	matches := placeholderRegex.FindAllStringSubmatchIndex(val, -1)
	var builder strings.Builder
	lastIndex := 0

	for _, match := range matches {
		start, end := match[0], match[1]
		varNameWithDefault := val[match[2]:match[3]]

		// 解析 varName 和 defaultVal
		var varName, defaultVal string
		if idx := strings.Index(varNameWithDefault, ":"); idx != -1 {
			varName = varNameWithDefault[:idx]
			defaultVal = varNameWithDefault[idx+1:]
		} else {
			varName = varNameWithDefault
		}

		envValue := valueMap[varName]
		// 如果变量不存在，则尝试从env中获取
		if envValue == "" {
			envValue = os.Getenv(varName)
		}

		// 如果仍然为空，则使用默认值
		if envValue == "" {
			envValue = defaultVal
		}
		// 如果变量仍然为空，则写入原始字符串
		if envValue == "" {
			builder.WriteString(val[lastIndex:end])
		} else {
			zlog.Debug(context.Background(), "Variable found", zap.String("variable", varName))
			builder.WriteString(val[lastIndex:start])
			builder.WriteString(envValue)
		}

		lastIndex = end
	}

	builder.WriteString(val[lastIndex:])
	return builder.String()
}

func RepairConfig(cfg *Config) error {
	if err := repairServiceIPWithNic(cfg); err != nil {
		return err
	}
	if cfg.Global.ReadBufferSize == nil {
		readerSize := codec.DefaultReaderSize
		cfg.Global.ReadBufferSize = &readerSize
	}
	codec.SetReaderSize(*cfg.Global.ReadBufferSize)

	const defaultIP = "0.0.0.0"
	setDefault(&cfg.Global.LocalIP, defaultIP)
	setDefault(&cfg.Server.Admin.IP, cfg.Global.LocalIP)

	for _, serviceCfg := range cfg.Server.Service {
		setDefault(&serviceCfg.Protocol, cfg.Server.Protocol)
		setDefault(&serviceCfg.Network, cfg.Server.Network)
		setDefault(&serviceCfg.IP, cfg.Global.LocalIP)
		setDefault(&serviceCfg.Transport, cfg.Server.Transport)
		setDefault(&serviceCfg.Address, net.JoinHostPort(serviceCfg.IP, strconv.Itoa(int(serviceCfg.Port))))

		if serviceCfg.Timeout == 0 {
			serviceCfg.Timeout = cfg.Server.Timeout
		}
	}

	setDefault(&cfg.Client.Namespace, cfg.Global.Namespace)
	for _, backendCfg := range cfg.Client.Service {
		repairClientConfig(backendCfg, &cfg.Client)
	}
	return nil
}

func repairServiceIPWithNic(cfg *Config) error {
	for index, item := range cfg.Server.Service {
		if item.IP == "" {
			ip := getIP(item.Nic)
			if ip == "" && item.Nic != "" {
				return fmt.Errorf("failed to get ip for nic %s", item.Nic)
			}
			cfg.Server.Service[index].IP = ip
		}
		setDefault(&cfg.Global.LocalIP, item.IP)
	}
	if cfg.Server.Admin.IP == "" {
		ip := getIP(cfg.Server.Admin.Nic)
		if ip == "" && cfg.Server.Admin.Nic != "" {
			return fmt.Errorf("can't find admin IP by the nic: %s", cfg.Server.Admin.Nic)
		}
		cfg.Server.Admin.IP = ip
	}
	return nil
}

func repairClientConfig(backendCfg *client.BackendConfig, clientCfg *ClientConfig) {
	setDefault(&backendCfg.Callee, backendCfg.ServiceName)
	setDefault(&backendCfg.ServiceName, backendCfg.Callee)
	setDefault(&backendCfg.Namespace, clientCfg.Namespace)
	setDefault(&backendCfg.Network, clientCfg.Network)
	setDefault(&backendCfg.Protocol, clientCfg.Protocol)
	setDefault(&backendCfg.Transport, clientCfg.Transport)
	// TODO: support service discovery
	// if backendCfg.Target == "" {
	// }
	if backendCfg.Timeout == 0 {
		backendCfg.Timeout = clientCfg.Timeout
	}
	backendCfg.Filter = deduplicate(clientCfg.Filter, backendCfg.Filter)
}

func setDefault(dst *string, def string) {
	if dst != nil && *dst == "" {
		*dst = def
	}
}

func getMillisecond(sec int) time.Duration {
	return time.Millisecond * time.Duration(sec)
}

var globalConfig atomic.Value

func init() {
	globalConfig.Store(defaultConfig())
}

func defaultConfig() *Config {
	const (
		defaultNetwork  = "tcp"
		defaultProtocol = "grpc"
		defaultTimeout  = int(5 * time.Second / time.Millisecond) // default 5 seconds
	)
	cfg := &Config{}
	cfg.Server.Network = defaultNetwork
	cfg.Server.Protocol = defaultProtocol
	cfg.Server.Timeout = defaultTimeout
	cfg.Client.Network = defaultNetwork
	cfg.Client.Protocol = defaultProtocol

	return cfg
}

func GlobalConfig() *Config {
	return globalConfig.Load().(*Config) // nolint
}

func SetGlobalConfig(cfg *Config) {
	globalConfig.Store(cfg)
}
