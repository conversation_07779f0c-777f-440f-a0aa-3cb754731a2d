package transport

import (
	"context"
	"reflect"
	"sync"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
)

var (
	svcTrans    = make(map[string]ServerTransport)
	muxSvrTrans = sync.RWMutex{}

	clientTrans    = make(map[string]ClientTransport)
	muxClientTrans = sync.RWMutex{}
)

type ServerTransport interface {
	ListenAndServe(ctx context.Context, opts ...ListenServeOption) error
}

type ClientTransport interface {
	RoundTrip(ctx context.Context, req []byte, opts ...RoundTripOption) ([]byte, error)
}

var DefaultServerTransport ServerTransport // TODO: add default transport

func RegisterServerTransport(name string, t ServerTransport) {
	tv := reflect.ValueOf(t)
	if t == nil || tv.Kind() == reflect.Ptr && tv.IsNil() {
		panic("transport: RegisterServerTransport server transport is nil")
	}
	if name == "" {
		panic("transport: RegisterServerTransport name is empty")
	}
	muxSvrTrans.Lock()
	svcTrans[name] = t
	muxSvrTrans.Unlock()
}

func GetServerTransport(name string) ServerTransport {
	muxSvrTrans.RLock()
	defer muxSvrTrans.RUnlock()
	return svcTrans[name]
}

func RegisterClientTransport(name string, t ClientTransport) {
	tv := reflect.ValueOf(t)
	if t == nil || tv.Kind() == reflect.Ptr && tv.IsNil() {
		panic("transport: RegisterClientTransport client transport is nil")
	}
	if name == "" {
		panic("transport: RegisterClientTransport name is empty")
	}
	muxClientTrans.Lock()
	clientTrans[name] = t
	muxClientTrans.Unlock()
}

func GetClientTransport(name string) ClientTransport {
	muxClientTrans.RLock()
	defer muxClientTrans.RUnlock()
	return clientTrans[name]
}

type Handler interface {
	Handle(ctx context.Context, req []byte) ([]byte, error)
}

var framerBuilders = make(map[string]codec.FramerBuilder)

func RegisterFramerBuilder(name string, fb codec.FramerBuilder) {
	fbv := reflect.ValueOf(fb)
	if fb == nil || fbv.Kind() == reflect.Ptr && fbv.IsNil() {
		panic("transport: RegisterFramerBuilder framer builder is nil")
	}
	if name == "" {
		panic("transport: RegisterFramerBuilder name is empty")
	}
	framerBuilders[name] = fb
}

func GetFramerBuilder(name string) codec.FramerBuilder {
	return framerBuilders[name]
}
