package transport

import (
	"time"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
)

type RoundTripOptions struct {
	Address       string // IP:Port
	Password      string
	Network       string // tcp/udp
	DialTimeout   time.Duration
	FramerBuilder codec.FramerBuilder
	Msg           codec.Msg
	Protocol      string

	CACertFile    string // CA certificate file
	TLSCertFile   string // client certificate file
	TLSKeyFile    string // client key file
	TLSServerName string // the name when client verifies the server, default as HTTP hostname
}

type RoundTripOption func(*RoundTripOptions)

func WithClientFramerBuilder(f codec.FramerBuilder) RoundTripOption {
	return func(o *RoundTripOptions) {
		o.FramerBuilder = f
	}
}

func WithProtocol(s string) RoundTripOption {
	return func(o *RoundTripOptions) {
		o.Protocol = s
	}
}

func WithDialNetwork(s string) RoundTripOption {
	return func(o *RoundTripOptions) {
		o.Network = s
	}
}

func WithDialPassword(s string) RoundTripOption {
	return func(o *RoundTripOptions) {
		o.Password = s
	}
}

func WithDialAddress(address string) RoundTripOption {
	return func(opts *RoundTripOptions) {
		opts.Address = address
	}
}

func WithDialTLS(certFile, keyFile, caFile, serverName string) RoundTripOption {
	return func(opts *RoundTripOptions) {
		opts.TLSCertFile = certFile
		opts.TLSKeyFile = keyFile
		opts.CACertFile = caFile
		opts.TLSServerName = serverName
	}
}
