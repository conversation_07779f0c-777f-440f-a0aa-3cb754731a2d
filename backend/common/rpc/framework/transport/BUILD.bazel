load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "transport",
    srcs = [
        "client_roundtrip.options.go",
        "client_transport.go",
        "client_transport_options.go",
        "server_listenserve_options.go",
        "server_transport_options.go",
        "transport.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport",
    visibility = ["//visibility:public"],
    deps = ["//backend/common/rpc/framework/codec"],
)
