package server

import (
	"time"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/filter"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport"
)

type Options struct {
	Namespace                string
	EnvName                  string
	ServiceName              string
	Address                  string
	Timeout                  time.Duration
	CurrentSerializationType int
	CurrentCompressType      int

	protocol string
	network  string

	ServeOptions []transport.ListenServeOption
	Transport    transport.ServerTransport
	Codec        codec.Codec

	Filters     filter.ServerChain
	FilterNames []string
}

type Option func(*Options)

func WithNamespace(namespace string) Option {
	return func(o *Options) {
		o.Namespace = namespace
	}
}

func WithEnvName(envName string) Option {
	return func(o *Options) {
		o.EnvName = envName
	}
}

func WithServiceName(s string) Option {
	return func(o *Options) {
		o.ServiceName = s
		o.ServeOptions = append(o.ServeOptions, transport.WithServiceName(s))
	}
}

func WithNamedFilter(name string, f filter.ServerFilter) Option {
	return func(o *Options) {
		o.Filters = append(o.Filters, f)
		o.FilterNames = append(o.FilterNames, name)
	}
}

func WithAddress(s string) Option {
	return func(o *Options) {
		o.ServeOptions = append(o.ServeOptions, transport.WithListenAddress(s))
		o.Address = s
	}
}

func WithNetwork(s string) Option {
	return func(o *Options) {
		o.network = s
		o.ServeOptions = append(o.ServeOptions, transport.WithListenNetwork(s))
	}
}

func WithTimeout(t time.Duration) Option {
	return func(o *Options) {
		o.Timeout = t
	}
}

func WithProtocol(s string) Option {
	return func(o *Options) {
		o.protocol = s
		o.Codec = codec.GetServer(s)
		fb := transport.GetFramerBuilder(s)
		if fb != nil {
			o.ServeOptions = append(o.ServeOptions, transport.WithServerFramerBuilder(fb))
		}
		trans := transport.GetServerTransport(s)
		if trans != nil {
			o.Transport = trans
		}
	}
}

func WithTransport(t transport.ServerTransport) Option {
	return func(o *Options) {
		if t != nil {
			o.Transport = t
		}
	}
}
