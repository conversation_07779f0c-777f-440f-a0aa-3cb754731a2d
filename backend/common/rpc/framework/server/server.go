package server

type Server struct {
	services map[string]Service
}

func (s *Server) AddService(serviceName string, service Service) {
	if s.services == nil {
		s.services = make(map[string]Service)
	}
	s.services[serviceName] = service
}

func (s *Server) Service(serviceName string) Service {
	if s.services == nil {
		return nil
	}
	return s.services[serviceName]
}

// TODO: graceful shutdown
func (s *Server) Serve() error {
	if len(s.services) == 0 {
		panic("no service registered")
	}

	for _, service := range s.services {
		go func(srv Service) {
			if err := srv.Serve(); err != nil {
				panic(err)
			}
		}(service)
	}

	select {}
}
