load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "server",
    srcs = [
        "options.go",
        "server.go",
        "service.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/framework/server",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/codec",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/filter",
        "//backend/common/rpc/framework/log",
        "//backend/common/rpc/framework/transport",
        "@org_golang_google_grpc//codes",
    ],
)
