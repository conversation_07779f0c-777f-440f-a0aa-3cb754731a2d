package server

import (
	"context"
	"errors"
	"reflect"

	"google.golang.org/grpc/codes"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/filter"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport"
)

type Service interface {
	Register(serviceDesc interface{}, serviceImpl interface{}) error
	Serve() error
	Close(chan struct{}) error
}

type FilterFunc func(reqBody interface{}) (filter.ServerChain, error)

type Handler func(ctx context.Context, f FilterFunc) (interface{}, error)

type Method struct {
	Name string
	Func func(svr interface{}, ctx context.Context, f FilterFunc) (rspBody interface{}, err error)
}

// ServiceDesc describes a proto service.
type ServiceDesc struct {
	ServiceName string
	HandlerType interface{}
	Methods     []Method
}

type service struct {
	ctx      context.Context
	cancel   context.CancelFunc
	opts     *Options
	handlers map[string]Handler
	// stopListening chan<- struct{}
}

func New(opts ...Option) Service {
	o := defaultOptions()
	s := &service{
		opts:     o,
		handlers: make(map[string]Handler),
	}
	for _, o := range opts {
		o(s.opts)
	}
	s.opts.ServeOptions = append(s.opts.ServeOptions, transport.WithHandler(s))
	s.ctx, s.cancel = context.WithCancel(context.Background())
	return s
}

func (s *service) Register(serviceDesc interface{}, serviceImpl interface{}) error {
	desc, ok := serviceDesc.(*ServiceDesc)
	if !ok {
		return errors.New("serviceDesc is not *ServiceDesc")
	}
	if serviceImpl != nil {
		ht := reflect.TypeOf(desc.HandlerType).Elem()
		hi := reflect.TypeOf(serviceImpl)
		if !hi.Implements(ht) {
			return errors.New("serviceImpl does not implement serviceDesc.HandlerType")
		}
	}
	for _, m := range desc.Methods {
		n := m.Name
		if _, ok := s.handlers[n]; ok {
			return errors.New("duplicate method name: " + n)
		}
		h := m.Func
		s.handlers[n] = func(ctx context.Context, f FilterFunc) (interface{}, error) {
			return h(serviceImpl, ctx, f)
		}
	}

	return nil
}

func (s *service) Serve() error {
	if err := s.opts.Transport.ListenAndServe(s.ctx, s.opts.ServeOptions...); err != nil {
		log.Errorf("failed to serve: %v", err)
		return err
	}
	log.Infof("%s service:%s launch success, %s:%s",
		s.opts.protocol, s.opts.ServiceName, s.opts.network, s.opts.Address)
	<-s.ctx.Done()
	return nil
}

// TODO: graceful shutdown
func (s *service) Close(ch chan struct{}) error {
	if ch == nil {
		ch = make(chan struct{}, 1)
	}
	s.cancel()
	ch <- struct{}{}
	return nil
}

func (s *service) Handle(ctx context.Context, reqBuf []byte) (rspBuf []byte, err error) {
	if s.opts.Codec == nil {
		return nil, errors.New("server codec empty")
	}

	msg := codec.Message(ctx)
	reqBodyBuf, err := s.decode(ctx, msg, reqBuf)
	if err != nil {
		return s.encode(ctx, msg, nil, err)
	}
	if err := msg.ServerRspErr(); err != nil {
		return s.encode(ctx, msg, nil, err)
	}
	rspbody, err := s.handle(ctx, msg, reqBodyBuf)
	if err != nil {
		return s.encode(ctx, msg, nil, err)
	}
	return s.handleResponse(ctx, msg, rspbody)
}

func (s *service) decode(_ context.Context, msg codec.Msg, reqBuf []byte) ([]byte, error) {
	s.setOpt(msg)
	reqBodyBuf, err := s.opts.Codec.Decode(msg, reqBuf)
	if err != nil {
		return nil, errs.NewFrameError(codes.DataLoss, "service codec Decode: "+err.Error())
	}
	s.setOpt(msg)
	return reqBodyBuf, nil
}

func (s *service) encode(_ context.Context, msg codec.Msg, rspBodyBuf []byte, e error) ([]byte, error) {
	if e != nil {
		msg.WithServerRspErr(e)
	}
	rspBuf, err := s.opts.Codec.Encode(msg, rspBodyBuf)
	if err != nil {
		return nil, err
	}
	return rspBuf, nil
}

func (s *service) setOpt(msg codec.Msg) {
	msg.WithNamespace(s.opts.Namespace)           // service namespace
	msg.WithEnvName(s.opts.EnvName)               // service environment
	msg.WithCalleeServiceName(s.opts.ServiceName) // from perspective of the service, callee refers to itself
}

func (s *service) handle(ctx context.Context, msg codec.Msg, reqBodyBuf []byte) (interface{}, error) {
	handler, ok := s.handlers[msg.ServerRPCName()]
	if !ok {
		handler, ok = s.handlers["*"]
		if !ok {
			return nil, errs.NewFrameError(codes.Unimplemented, "service method not found: "+msg.ServerRPCName())
		}
	}

	timeout := s.opts.Timeout
	if timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	}
	newFilterFunc := s.filterFunc(ctx, msg, reqBodyBuf)
	rspBody, err := handler(ctx, newFilterFunc)
	if err != nil {
		return nil, err
	}
	return rspBody, nil
}

func (s *service) filterFunc(_ context.Context, msg codec.Msg, reqBodyBuf []byte) FilterFunc {
	return func(reqBody interface{}) (filter.ServerChain, error) {
		compressType := msg.CompressType()
		if compressType < 0 {
			compressType = s.opts.CurrentCompressType
		}
		reqBodyBuf, err := codec.Decompress(compressType, reqBodyBuf) // nolint
		if err != nil {
			return nil, errs.NewFrameError(codes.DataLoss, "service decompress: "+err.Error())
		}
		serializationType := msg.SerializationType()
		if serializationType < 0 {
			serializationType = s.opts.CurrentSerializationType
		}
		err = codec.Unmarshal(serializationType, reqBodyBuf, reqBody)
		if err != nil {
			return nil, errs.NewFrameError(codes.DataLoss, "service unmarshal: "+err.Error())
		}
		return s.opts.Filters, nil
	}
}

func (s *service) handleResponse(ctx context.Context, msg codec.Msg, rspBody interface{}) ([]byte, error) {
	serializationType := msg.SerializationType()
	if serializationType < 0 {
		serializationType = s.opts.CurrentSerializationType
	}
	rspBodyBuf, err := codec.Marshal(serializationType, rspBody)
	if err != nil {
		err = errs.NewFrameError(codes.DataLoss, "service codec Marshal: "+err.Error())
		return s.encode(ctx, msg, rspBodyBuf, err)
	}

	compressType := msg.CompressType()
	if compressType < 0 {
		compressType = s.opts.CurrentCompressType
	}
	rspBodyBuf, err = codec.Compress(compressType, rspBodyBuf)
	if err != nil {
		err = errs.NewFrameError(codes.DataLoss, "service codec Compress: "+err.Error())
		return s.encode(ctx, msg, rspBodyBuf, err)
	}
	rspBuf, err := s.encode(ctx, msg, rspBodyBuf, nil)
	return rspBuf, err
}

func defaultOptions() *Options {
	const (
		invalidSerializationType = -1
		invalidCompressionType   = -1
	)
	return &Options{
		protocol:                 "unknown",
		ServiceName:              "unknown",
		CurrentSerializationType: invalidSerializationType,
		CurrentCompressType:      invalidCompressionType,
		Transport:                transport.DefaultServerTransport,
	}
}
