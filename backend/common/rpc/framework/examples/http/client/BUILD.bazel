load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "client_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/framework/examples/http/client",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/client",
        "//backend/common/rpc/framework/http",
    ],
)

go_binary(
    name = "client",
    embed = [":client_lib"],
    visibility = ["//visibility:public"],
)
