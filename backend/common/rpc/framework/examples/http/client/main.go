package main

import (
	"context"
	"fmt"

	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/client"
	mhttp "github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
)

func main() {
	framework.ServerConfigPath = "/Users/<USER>/moego/moego/backend/common/rpc/framework/examples/http/client/config.yaml"
	_ = framework.NewServer()
	cli := mhttp.NewClientProxy("moego.ark.test")
	req := struct {
		Msg string
	}{
		Msg: "ark",
	}
	rsp := struct {
		Msg string
	}{}
	err := cli.Patch(context.Background(), "/ark", req, &rsp, client.WithReqHead(&mhttp.ClientReqHeader{}))
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(rsp)
}
