load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "server_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/framework/examples/http/server",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/http",
        "@com_github_gorilla_mux//:mux",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
    visibility = ["//visibility:public"],
)
