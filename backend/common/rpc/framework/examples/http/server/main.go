package main

import (
	"io"
	"net/http"

	"github.com/gorilla/mux"

	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	mhttp "github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
)

func main() {
	framework.ServerConfigPath = "/Users/<USER>/moego/moego/backend/common/rpc/framework/examples/http/server/config.yaml"
	s := framework.NewServer()
	router := mux.NewRouter()
	router.HandleFunc("/ark", handle).Methods(http.MethodPost)
	mhttp.RegisterNoProtocolServiceMux(s.Service("moego.ark.test"), router)
	s.Serve()
}

func handle(w http.ResponseWriter, r *http.Request) {
	// handle 的编写方法完全同标准库 HTTP 的使用方式一致
	// 比如可以在 r 中读取 Header 等
	// 可以在 r.Body 对 client 进行流式读包
	_, err := io.ReadAll(r.Body)
	if err != nil { /*..*/
	}
	// 最后使用 w 来进行回包
	w.Header().Set("Content-type", "application/text")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("{\"msg\":\"response body\"}"))
}
