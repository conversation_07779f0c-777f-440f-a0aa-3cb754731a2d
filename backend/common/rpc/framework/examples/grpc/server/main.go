package main

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	helloworld "github.com/MoeGolibrary/moego/backend/proto/helloworld/v1"
)

func main() {
	rpc.ServerConfigPath = "/Users/<USER>/moego/moego/backend/common/rpc/framework/examples/grpc/server/config.yaml"
	s := rpc.NewServer()
	grpc.Register(s, &helloworld.HelloworldService_ServiceDesc, &Greeter{})
	grpc.Register(s, &helloworld.EchoService_ServiceDesc, &EchoService{})
	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}

type Greeter struct {
	helloworld.UnimplementedHelloworldServiceServer
}

func (g Greeter) SendPing(ctx context.Context, req *helloworld.SendPingRequest) (*helloworld.SendPingResponse, error) {
	log.DebugContextf(ctx, "Received: %v", req.Ping)
	return &helloworld.SendPingResponse{Pong: "Pong " + req.Ping + "!"}, nil
}

type EchoService struct {
	helloworld.UnimplementedEchoServiceServer
}

func (s EchoService) Echo(ctx context.Context, req *helloworld.EchoRequest) (*helloworld.EchoResponse, error) {
	log.DebugContextf(ctx, "Echo: %v", req.GetEcho())
	return &helloworld.EchoResponse{Echo: req.GetEcho()}, nil
}
