package main

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	helloworld "github.com/MoeGolibrary/moego/backend/proto/helloworld/v1"
)

func main() {
	_ = rpc.NewServer()

	c := grpc.NewClient("greeter_cutomized_name", helloworld.NewHelloworldServiceClient)

	ctx := context.Background()
	rsp, err := c.SendPing(ctx, &helloworld.SendPingRequest{Ping: "chi"})
	if err != nil {
		log.ErrorContextf(ctx, "failed to call SendPing: %s", err.<PERSON>rror())
	}
	log.DebugContextf(ctx, "SendPing response: %s", rsp.Pong)
}
