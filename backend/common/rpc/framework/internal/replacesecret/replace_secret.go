package replacesecret

func ReplaceSecret(s []byte, secrets map[string]string) []byte {
	var buf []byte
	i := 0
	for j := 0; j < len(s); j++ {
		if s[j] == '$' && j+2 < len(s) && s[j+1] == '{' {
			if buf == nil {
				buf = make([]byte, 0, 2*len(s))
			}
			buf = append(buf, s[i:j]...)
			name, w := getKeyName(s[j+1:])
			if name == nil && w > 0 {
			} else if name == nil {
				buf = append(buf, s[j])
			} else {
				buf = append(buf, secrets[string(name)]...)
			}
			j += w
			i = j + 1
		}
	}
	if buf == nil {
		return s
	}
	return append(buf, s[i:]...)
}

func getKeyName(s []byte) ([]byte, int) {
	for i := 1; i < len(s); i++ {
		if s[i] == ' ' || s[i] == '\n' || s[i] == '"' {
			return nil, 0
		}
		if s[i] == '}' {
			if i == 1 {
				return nil, 2
			}
			return s[1:i], i + 1
		}
	}
	return nil, 0
}
