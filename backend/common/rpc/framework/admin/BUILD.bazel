load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "admin",
    srcs = [
        "admin.go",
        "config.go",
        "mux.go",
        "options.go",
        "router.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/framework/admin",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/healthcheck",
        "//backend/common/rpc/framework/log",
        "@in_gopkg_yaml_v3//:yaml_v3",
    ],
)
