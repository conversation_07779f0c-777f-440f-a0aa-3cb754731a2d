package admin

import (
	"time"
)

const (
	defaultListenAddr   = "127.0.0.1:9028" // Default listening port.
	defaultReadTimeout  = time.Second * 3
	defaultWriteTimeout = time.Second * 60
	defaultSkipServe    = false
)

func newDefaultConfig() *configuration {
	return &configuration{
		skipServe:    defaultSkipServe,
		addr:         defaultListenAddr,
		readTimeout:  defaultReadTimeout,
		writeTimeout: defaultWriteTimeout,
	}
}

// configuration manages trpc service configuration.
type configuration struct {
	addr         string
	readTimeout  time.Duration
	writeTimeout time.Duration
	configPath   string
	skipServe    bool
}
