package framework

import (
	"fmt"

	"go.uber.org/automaxprocs/maxprocs"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/admin"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/filter"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/server"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport"
)

func NewServer(opt ...server.Option) *server.Server {
	cfg, err := LoadConfig(serverConfigPath())
	if err != nil {
		panic(err)
	}

	log.Infof("config loaded success")

	SetGlobalConfig(cfg)

	_, err = SetupPlugins(cfg.Plugins)
	if err != nil {
		panic(err)
	}
	if err := SetupClients(&cfg.Client); err != nil {
		panic("failed to setup clients: " + err.Error())
	}

	_, _ = maxprocs.Set(maxprocs.Logger(log.Debugf))
	s := NewServerWithConfig(cfg, opt...)
	return s
}

func NewServerWithConfig(cfg *Config, opt ...server.Option) *server.Server {
	if err := RepairConfig(cfg); err != nil {
		panic(err)
	}

	SetGlobalConfig(cfg)

	s := &server.Server{}

	setupAdmin(s, cfg)

	for _, c := range cfg.Server.Service {
		s.AddService(c.Name, newServiceWithConfig(cfg, c, opt...))
	}
	return s
}

func setupAdmin(s *server.Server, cfg *Config) {
	// admin configured, then admin service will be started
	opts := []admin.Option{
		admin.WithSkipServe(cfg.Server.Admin.Port == 0),
		admin.WithConfigPath(ServerConfigPath),
		admin.WithReadTimeout(getMillisecond(cfg.Server.Admin.ReadTimeout)),
		admin.WithWriteTimeout(getMillisecond(cfg.Server.Admin.WriteTimeout)),
	}
	if cfg.Server.Admin.Port > 0 {
		opts = append(opts, admin.WithAddr(fmt.Sprintf("%s:%d", cfg.Server.Admin.IP, cfg.Server.Admin.Port)))
	}
	s.AddService(admin.ServiceName, admin.NewServer(opts...))
}

func newServiceWithConfig(cfg *Config, serviceCfg *ServiceConfig, opt ...server.Option) server.Service {
	var (
		filters     filter.ServerChain
		filterNames []string
	)
	for _, name := range deduplicate(cfg.Server.Filter, serviceCfg.Filter) {
		f := filter.GetServer(name)
		if f == nil {
			panic(fmt.Sprintf("filter %s no registered, do not configure", name))
		}
		filters = append(filters, f)
		filterNames = append(filterNames, name)
	}

	opts := []server.Option{
		server.WithNamespace(cfg.Global.Namespace),
		server.WithEnvName(cfg.Global.EnvName),
		server.WithServiceName(serviceCfg.Name),
		server.WithProtocol(serviceCfg.Protocol),
		server.WithTransport(transport.GetServerTransport(serviceCfg.Transport)),
		server.WithNetwork(serviceCfg.Network),
		server.WithAddress(serviceCfg.Address),
		server.WithTimeout(getMillisecond(serviceCfg.Timeout)),
	}
	for i := range filters {
		opts = append(opts, server.WithNamedFilter(filterNames[i], filters[i]))
	}

	opts = append(opts, opt...)
	return server.New(opts...)
}
