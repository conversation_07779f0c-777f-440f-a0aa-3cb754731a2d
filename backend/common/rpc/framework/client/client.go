package client

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport"
)

type Client interface {
	Invoke(ctx context.Context, reqBody interface{}, rspBody interface{}, opt ...Option) error
}

var DefaultClient = New()

var New = func() Client {
	return &client{}
}

type client struct{}

func (c *client) Invoke(ctx context.Context, reqBody interface{}, rspBody interface{}, opt ...Option) error {
	ctx, msg := codec.EnsureMessage(ctx)

	opts, err := c.getOptions(msg, opt...)
	if err != nil {
		return err
	}

	c.updateMsg(msg, opts)

	if opts.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, opts.Timeout)
		defer cancel()
	}
	if deadline, ok := ctx.Deadline(); ok {
		msg.WithRequestTimeout(time.Until(deadline))
	}

	return opts.Filters.Filter(contextWithOptions(ctx, opts), reqBody, rspBody, callFunc)
}

func (c *client) getOptions(msg codec.Msg, opt ...Option) (*Options, error) {
	opts := getOptionsByCalleeAndUserOptions(msg.CalleeServiceName(), opt...).clone()
	for _, o := range opt {
		o(opts)
	}
	if err := opts.parseTarget(); err != nil {
		return nil, err
	}
	opts.CallOptions = append(opts.CallOptions, transport.WithDialAddress(opts.endpoint))
	return opts, nil
}

func (c *client) updateMsg(msg codec.Msg, opts *Options) {
	if opts.ServiceName != "" {
		msg.WithCalleeServiceName(opts.ServiceName)
	}
	if opts.CalleeMethod != "" {
		msg.WithCalleeMethod(opts.CalleeMethod)
	}
	if len(opts.MetaData) > 0 {
		msg.WithClientMetaData(c.getMetaData(msg, opts))
	}
	if opts.CallerServiceName != "" {
		msg.WithCallerServiceName(opts.CallerServiceName)
	}
	if opts.SerializationType >= 0 {
		msg.WithSerializationType(opts.SerializationType)
	}
	if opts.CompressType >= 0 {
		msg.WithCompressType(opts.CompressType)
	}
	if opts.ReqHead != nil {
		msg.WithClientReqHead(opts.ReqHead)
	}
	if opts.RspHead != nil {
		msg.WithClientRspHead(opts.RspHead)
	}
	if opts.CurrentSerializationType >= 0 {
		msg.WithSerializationType(opts.CurrentSerializationType)
	}
}

func (c *client) getMetaData(msg codec.Msg, opts *Options) codec.MetaData {
	md := msg.ClientMetaData()
	if md == nil {
		md = codec.MetaData{}
	}
	for k, v := range opts.MetaData {
		md[k] = v
	}
	return md
}

func callFunc(ctx context.Context, reqBody interface{}, rspBody interface{}) error {
	msg := codec.Message(ctx)
	opts := OptionsFromContext(ctx)

	if opts.Codec == nil {
		return errs.NewFrameError(codes.DataLoss, "client: codec is nil")
	}

	reqBuf, err := prepareRequestBuf(ctx, msg, reqBody, opts)
	if err != nil {
		return err
	}
	rspBuf, err := opts.Transport.RoundTrip(ctx, reqBuf, opts.CallOptions...)
	if err != nil {
		return err
	}

	return processResponseBuf(ctx, msg, rspBody, rspBuf, opts)
}

func prepareRequestBuf(ctx context.Context, msg codec.Msg, reqBody interface{}, opts *Options) ([]byte, error) {
	reqBodyBuf, err := serializeAndCompress(ctx, msg, reqBody, opts)
	if err != nil {
		return nil, err
	}

	reqBuf, err := opts.Codec.Encode(msg, reqBodyBuf)
	if err != nil {
		return nil, errs.NewFrameError(codes.DataLoss, "client codec Encode: "+err.Error())
	}

	return reqBuf, nil
}

func serializeAndCompress(_ context.Context, msg codec.Msg, reqBody any, opts *Options) ([]byte, error) {
	serializationType := msg.SerializationType()
	if opts.CurrentSerializationType >= 0 {
		serializationType = opts.CurrentSerializationType
	}
	var (
		reqBodyBuf []byte
		err        error
	)
	if serializationType >= 0 {
		reqBodyBuf, err = codec.Marshal(serializationType, reqBody)
	}
	if err != nil {
		return nil, errs.NewFrameError(codes.DataLoss, "client codec Marshal: "+err.Error())
	}

	compressType := msg.CompressType()
	if opts.CurrentCompressType >= 0 {
		compressType = opts.CurrentCompressType
	}
	reqBodyBuf, err = codec.Compress(compressType, reqBodyBuf)
	if err != nil {
		return nil, errs.NewFrameError(codes.DataLoss, "client codec Compress: "+err.Error())
	}
	return reqBodyBuf, nil
}

func processResponseBuf(_ context.Context, msg codec.Msg, rspBody interface{}, rspBodyBuf []byte, opts *Options) error {
	rspBodyBuf, err := opts.Codec.Decode(msg, rspBodyBuf)
	if err != nil {
		return errs.NewFrameError(codes.DataLoss, "client codec Decode: "+err.Error())
	}
	if msg.ClientRspErr() != nil {
		return msg.ClientRspErr()
	}
	if len(rspBodyBuf) == 0 {
		return nil
	}

	compressType := msg.CompressType()
	if opts.CurrentCompressType >= 0 {
		compressType = opts.CurrentCompressType
	}
	rspBodyBuf, err = codec.Decompress(compressType, rspBodyBuf)
	if err != nil {
		return errs.NewFrameError(codes.DataLoss, "client codec Decompress: "+err.Error())
	}

	serializationType := msg.SerializationType()
	if opts.CurrentSerializationType >= 0 {
		serializationType = opts.CurrentSerializationType
	}
	err = codec.Unmarshal(serializationType, rspBodyBuf, rspBody)
	if err != nil {
		return errs.NewFrameError(codes.DataLoss, "client codec Unmarshal: "+err.Error())
	}

	return nil
}
