package client

import (
	"fmt"
	"sync"
	"time"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/filter"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport"
)

type BackendConfig struct {
	Callee        string `yaml:"callee"`
	ServiceName   string `yaml:"name"`
	EnvName       string `yaml:"env_name"`
	Namespace     string `yaml:"namespace"`
	Target        string `yaml:"target"`
	Password      string `yaml:"password"`
	Network       string `yaml:"network"`
	Timeout       int    `yaml:"timeout"`
	Protocol      string `yaml:"protocol"`
	Transport     string `yaml:"transport"`
	Serialization *int   `yaml:"serialization"`
	Compression   int    `yaml:"compression"`
	TLSKey        string `yaml:"tls_key"`
	TLSCert       string `yaml:"tls_cert"`
	// 如果 ca_cert 设置为 none，则会开启 InsecureSkipVerify = true，并将 http 请求替换为 https
	CACert        string   `yaml:"ca_cert"`
	TLSServerName string   `yaml:"tls_server_name"`
	Filter        []string `yaml:"filter"`
}

func (cfg *BackendConfig) genOptions() (*Options, error) {
	opts := NewOptions()
	if cfg.Target != "" {
		opts.Target = cfg.Target
	}
	if cfg.Timeout > 0 {
		opts.Timeout = time.Duration(cfg.Timeout) * time.Millisecond
	}
	if cfg.Serialization != nil {
		opts.SerializationType = *cfg.Serialization
	}
	if cfg.Compression > 0 {
		opts.CompressType = cfg.Compression
	}

	opts.Transport = nil
	WithTransport(transport.GetClientTransport(cfg.Transport))(opts)
	WithProtocol(cfg.Protocol)(opts)
	WithNetwork(cfg.Network)(opts)
	if opts.Transport == nil {
		opts.Transport = transport.DefaultClientTransport
	}
	WithPassword(cfg.Password)(opts)
	WithTLS(cfg.TLSCert, cfg.TLSKey, cfg.CACert, cfg.TLSServerName)(opts)
	if cfg.Protocol != "" && opts.Codec == nil {
		return nil, fmt.Errorf("codec is not set for protocol %s", cfg.Protocol)
	}
	for _, name := range cfg.Filter {
		f := filter.GetClient(name)
		if f == nil {
			return nil, fmt.Errorf("filter %s not found", name)
		}
		opts.Filters = append(opts.Filters, f)
	}
	opts.rebuildSliceCapacity()

	return opts, nil
}

var (
	defaultBackendConf = &BackendConfig{
		Network:  "tcp",
		Protocol: "grpc",
	}
	defaultBackendOptions *Options

	mutex   sync.RWMutex
	configs = make(map[string]*configsWithFallback)
	options = make(map[string]*optionsWithFallback)
)

type configsWithFallback struct {
	fallback     *BackendConfig
	serviceNames map[string]*BackendConfig
}

type optionsWithFallback struct {
	fallback     *Options
	serviceNames map[string]*Options
}

func getDefaultOptions() *Options {
	mutex.RLock()
	opts := defaultBackendOptions
	mutex.RUnlock()
	if opts != nil {
		return opts
	}

	mutex.Lock()
	defer mutex.Unlock()
	if defaultBackendOptions != nil {
		return defaultBackendOptions
	}
	opts, err := defaultBackendConf.genOptions()
	if err != nil {
		defaultBackendOptions = NewOptions()
	} else {
		defaultBackendOptions = opts
	}
	return defaultBackendOptions
}

func DefaultClientConfig() map[string]*BackendConfig {
	mutex.RLock()
	c := make(map[string]*BackendConfig)
	for k, v := range configs {
		c[k] = v.fallback
	}
	mutex.RUnlock()
	return c
}

func getOptionsByCalleeAndUserOptions(callee string, opt ...Option) *Options {
	intputOpts := &Options{}
	for _, o := range opt {
		o(intputOpts)
	}
	if intputOpts.ServiceName != "" {
		return getOptionsByCalleeAndServiceName(callee, intputOpts.ServiceName)
	}
	return getOptions(callee)
}

func getOptionsByCalleeAndServiceName(callee, serviceName string) *Options {
	mutex.RLock()
	defer mutex.RUnlock()
	serviceOptions, ok := options[callee]
	if !ok {
		return getOptions(callee)
	}
	opts, ok := serviceOptions.serviceNames[serviceName]
	if !ok {
		return getOptions(callee)
	}
	return opts
}

func getOptions(callee string) *Options {
	mutex.RLock()
	defer mutex.RUnlock()
	if len(options) == 0 {
		return getDefaultOptions()
	}
	opts, ok := options[callee]
	if !ok {
		opts, ok = options["*"]
		if !ok {
			return getDefaultOptions()
		}
	}
	return opts.fallback
}

func RegisterClientConfig(callee string, conf *BackendConfig) error {
	if callee == "*" {
		conf.Callee = ""
		conf.ServiceName = ""
	}
	opts, err := conf.genOptions()
	if err != nil {
		return err
	}

	mutex.Lock()
	defer mutex.Unlock()
	if opt, ok := options[callee]; !ok || opt == nil {
		options[callee] = &optionsWithFallback{
			serviceNames: make(map[string]*Options),
		}
		configs[callee] = &configsWithFallback{
			serviceNames: make(map[string]*BackendConfig),
		}
	}
	options[callee].fallback = opts
	configs[callee].fallback = conf
	options[callee].serviceNames[conf.ServiceName] = opts
	configs[callee].serviceNames[conf.ServiceName] = conf

	return nil
}
