# Moego 错误处理包

这个包提供了一个统一的错误处理机制，支持业务错误和框架错误的处理，并与 gRPC 错误码系统集成。

## ⚠️ 重要约束

**所有业务错误码必须定义在 protobuf 文件中**。这是一个强制性的约束，原因如下：

1. 确保错误码的唯一性和一致性
2. 便于跨服务错误码的统一管理
3. 支持错误码的自动生成和类型安全
4. 便于错误码的版本控制和文档管理

## 特性

- 支持业务错误和框架错误的区分
- 与 gRPC 错误码系统集成
- 支持错误链追踪
- 支持错误堆栈信息
- 支持错误码注册和管理
- 支持格式化错误消息

## 错误类型

包定义了三种错误类型：

```go
const (
    ErrorTypeFramework       = 1  // 框架错误
    ErrorTypeBusiness        = 2  // 业务错误
    ErrorTypeCalleeFramework = 3  // 下游服务框架错误
)
```

## 基本用法

### 1. 创建业务错误

```go
// 使用预定义的错误码
err := errs.New(codes.NotFound)  // 使用 gRPC 标准错误码

// 使用自定义错误码和消息
err := errs.Newm(yourErrorCode, "自定义错误消息")

// 使用格式化消息
err := errs.Newf(yourErrorCode, "用户 %s 不存在", username)
```

### 2. 包装已有错误

```go
// 包装错误并添加新的错误码和消息
err := errs.Wrap(originalErr, yourErrorCode, "包装的错误消息")

// 使用格式化消息包装错误
err := errs.Wrapf(originalErr, yourErrorCode, "操作失败: %v", originalErr)
```

### 3. 创建框架错误

```go
// 创建框架错误
err := errs.NewFrameError(codes.Internal, "内部服务错误")

// 包装错误为框架错误
err := errs.WrapFrameError(originalErr, codes.Internal, "内部服务错误")
```

### 4. 错误处理

```go
// 获取错误码
code := errs.Code(err)

// 获取错误消息
msg := errs.Msg(err)

// 判断是否是超时错误
if e, ok := err.(*errs.Error); ok && e.IsTimeout(errs.ErrorTypeBusiness) {
    // 处理超时错误
}
```

## 错误码注册

### 1. 错误码定义

所有业务错误码必须在 protobuf 文件中定义，例如在 `customer.proto` 中：

```protobuf
enum ErrCode {
  // 成功
  ERR_CODE_OK = 0;
  // 本服务自动分配的全局唯一的起始错误码
  ERR_CODE_UNSPECIFIED = 119500;
  
  // 客户相关错误码 (119501-119599)
  ERR_CODE_CUSTOMER_NOT_FOUND = 119501;    // 客户不存在
  ERR_CODE_CUSTOMER_ALREADY_EXISTS = 119502;  // 客户已存在
  ERR_CODE_INVALID_CUSTOMER_ID = 119503;   // 无效的客户ID
  ERR_CODE_INVALID_CUSTOMER_NAME = 119504; // 无效的客户名称
  ERR_CODE_CUSTOMER_DELETED = 119505;      // 客户已删除
  
  // 地址相关错误码 (119601-119699)
  ERR_CODE_ADDRESS_NOT_FOUND = 119601;     // 地址不存在
  ERR_CODE_INVALID_ADDRESS = 119602;       // 无效的地址信息
  ERR_CODE_ADDRESS_LIMIT_EXCEEDED = 119603; // 超出地址数量限制
  
  // 任务相关错误码 (119701-119799)
  ERR_CODE_TASK_NOT_FOUND = 119701;        // 任务不存在
  ERR_CODE_TASK_ALREADY_COMPLETED = 119702; // 任务已完成
  ERR_CODE_INVALID_TASK_STATUS = 119703;    // 无效的任务状态
}
```

### 2. 注册业务错误码

在项目启动时，需要注册 protobuf 中定义的错误码：

```go
func init() {
    errs.Register(map[int32]string{
        int32(customerpb.ErrCode_ERR_CODE_OK): "成功",
        int32(customerpb.ErrCode_ERR_CODE_UNSPECIFIED): "未知错误",
        
        // 客户相关错误码
        int32(customerpb.ErrCode_ERR_CODE_CUSTOMER_NOT_FOUND): "客户不存在",
        int32(customerpb.ErrCode_ERR_CODE_CUSTOMER_ALREADY_EXISTS): "客户已存在",
        int32(customerpb.ErrCode_ERR_CODE_INVALID_CUSTOMER_ID): "无效的客户ID",
        int32(customerpb.ErrCode_ERR_CODE_INVALID_CUSTOMER_NAME): "无效的客户名称",
        int32(customerpb.ErrCode_ERR_CODE_CUSTOMER_DELETED): "客户已删除",
        
        // 地址相关错误码
        int32(customerpb.ErrCode_ERR_CODE_ADDRESS_NOT_FOUND): "地址不存在",
        int32(customerpb.ErrCode_ERR_CODE_INVALID_ADDRESS): "无效的地址信息",
        int32(customerpb.ErrCode_ERR_CODE_ADDRESS_LIMIT_EXCEEDED): "超出地址数量限制",
        
        // 任务相关错误码
        int32(customerpb.ErrCode_ERR_CODE_TASK_NOT_FOUND): "任务不存在",
        int32(customerpb.ErrCode_ERR_CODE_TASK_ALREADY_COMPLETED): "任务已完成",
        int32(customerpb.ErrCode_ERR_CODE_INVALID_TASK_STATUS): "无效的任务状态",
    })
}
```

注册错误码后，可以直接使用 `errs.New` 创建错误，不需要重复写错误消息。如果错误消息需要动态内容，才使用 `errs.Newm` 或 `errs.Newf`。

### 3. 使用示例

#### 示例1：使用 errs.New（适用于已注册的错误码）

```go
// 在 init 中注册错误码
func init() {
    errs.Register(map[int32]string{
        int32(customerpb.ErrCode_ERR_CODE_CUSTOMER_NOT_FOUND): "客户不存在",
        int32(customerpb.ErrCode_ERR_CODE_INVALID_CUSTOMER_ID): "无效的客户ID",
    })
}

// 在业务代码中使用
func (s *Service) GetCustomer(ctx context.Context, req *customerpb.GetCustomerRequest) (*customerpb.Customer, error) {
    if req.CustomerId <= 0 {
        // 直接使用 errs.New，因为错误消息已经在 init 中注册
        return nil, errs.New(customerpb.ErrCode_ERR_CODE_INVALID_CUSTOMER_ID)
    }

    customer, err := s.repo.GetCustomer(ctx, req.CustomerId)
    if err != nil {
        if errors.Is(err, sql.ErrNoRows) {
            // 直接使用 errs.New，因为错误消息已经在 init 中注册
            return nil, errs.New(customerpb.ErrCode_ERR_CODE_CUSTOMER_NOT_FOUND)
        }
        return nil, errs.Wrap(err, customerpb.ErrCode_ERR_CODE_UNSPECIFIED, "获取客户信息失败")
    }

    return customer, nil
}
```

#### 示例2：使用 errs.Newm（适用于需要动态错误消息的场景）

```go
// 在业务代码中使用
func (s *Service) UpdateCustomerName(ctx context.Context, req *customerpb.UpdateCustomerRequest) error {
    // 检查名称长度
    if len(req.Name) > 50 {
        // 使用 errs.Newm 提供动态的错误消息
        return errs.Newm(
            customerpb.ErrCode_ERR_CODE_INVALID_CUSTOMER_NAME,
            "客户名称长度不能超过50个字符，当前长度：%d",
            len(req.Name),
        )
    }

    // 检查名称是否包含敏感词
    if s.containsSensitiveWord(req.Name) {
        // 使用 errs.Newm 提供具体的错误原因
        return errs.Newm(
            customerpb.ErrCode_ERR_CODE_INVALID_CUSTOMER_NAME,
            "客户名称包含敏感词：%s",
            s.getSensitiveWord(req.Name),
        )
    }

    // 更新客户名称
    err := s.repo.UpdateCustomerName(ctx, req.CustomerId, req.Name)
    if err != nil {
        return errs.Wrap(err, customerpb.ErrCode_ERR_CODE_UNSPECIFIED, "更新客户名称失败")
    }

    return nil
}
```

#### 示例3：errs.Newm 覆盖已注册的错误消息

```go
// 在 init 中注册错误码
func init() {
    errs.Register(map[int32]string{
        int32(customerpb.ErrCode_ERR_CODE_INVALID_CUSTOMER_NAME): "无效的客户名称",
    })
}

// 在业务代码中使用
func (s *Service) ValidateCustomerName(ctx context.Context, name string) error {
    // 即使错误码已经在 init 中注册了错误消息
    // errs.Newm 中的错误消息会覆盖已注册的消息
    if name == "" {
        return errs.Newm(
            customerpb.ErrCode_ERR_CODE_INVALID_CUSTOMER_NAME,
            "客户名称不能为空",
        )
    }

    if len(name) < 2 {
        return errs.Newm(
            customerpb.ErrCode_ERR_CODE_INVALID_CUSTOMER_NAME,
            "客户名称长度不能小于2个字符",
        )
    }

    // 使用 errs.New 会使用 init 中注册的错误消息
    if !s.isValidName(name) {
        return errs.New(customerpb.ErrCode_ERR_CODE_INVALID_CUSTOMER_NAME)
    }

    return nil
}
```

这两个示例展示了：
1. 当错误消息是固定的，且已在 init 中注册时，使用 `errs.New`
2. 当错误消息需要包含动态内容时，使用 `errs.Newm`
3. 当需要包装原始错误时，使用 `errs.Wrap`
4. `errs.Newm` 中的错误消息会覆盖已注册的错误消息

注意：
1. 不要直接在代码中定义错误码，必须使用 protobuf 生成的错误码
2. 如果错误消息已经在 init 中注册，直接使用 `errs.New` 即可
3. 只有在需要动态错误消息时，才使用 `errs.Newm` 或 `errs.Newf`
4. `errs.Newm` 中的错误消息会覆盖已注册的错误消息，这可以用于提供更具体的错误信息

## 最佳实践

1. 在项目启动时注册所有业务错误码
2. 使用 `Wrap` 或 `Wrapf` 保留原始错误信息
3. 对于框架级别的错误，使用 `NewFrameError` 或 `WrapFrameError`
4. 在错误处理时，先检查错误类型，再获取错误码和消息

## 示例

### 业务代码示例

```go
func GetUser(userID string) (*User, error) {
    if userID == "" {
        return nil, errs.Newm(pb.ErrorCode_INVALID_PARAMS, "用户ID不能为空")
    }
    
    user, err := db.GetUser(userID)
    if err != nil {
        if errors.Is(err, sql.ErrNoRows) {
            return nil, errs.Newm(pb.ErrorCode_USER_NOT_FOUND, "用户不存在")
        }
        return nil, errs.Wrap(err, pb.ErrorCode_INTERNAL_ERROR, "获取用户信息失败")
    }
    
    return user, nil
}
```

### 错误处理示例

```go
func HandleError(err error) {
    if err == nil {
        return
    }
    
    code := errs.Code(err)
    msg := errs.Msg(err)
    
    // 根据错误码处理不同类型的错误
    switch code {
    case int32(pb.ErrorCode_USER_NOT_FOUND):
        // 处理用户不存在错误
    case int32(pb.ErrorCode_INVALID_PARAMS):
        // 处理参数无效错误
    default:
        // 处理其他错误
    }
}
``` 