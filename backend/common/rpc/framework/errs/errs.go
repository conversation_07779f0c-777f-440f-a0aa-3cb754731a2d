package errs

import (
	"errors"
	"fmt"
	"io"

	spb "google.golang.org/genproto/googleapis/rpc/status"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

const (
	// Success is the success prompt string.
	Success = "success"
)

// ErrorType is the error code type, including framework error code and business error code.
const (
	ErrorTypeUnknown         = 0
	ErrorTypeFramework       = 1
	ErrorTypeBusiness        = 2
	ErrorTypeCalleeFramework = 3 // The error code returned by the client call
	// represents the downstream framework error code.
)

func typeDesc(t int) string {
	switch t {
	case ErrorTypeFramework:
		return "framework"
	case ErrorTypeCalleeFramework:
		return "callee framework"
	default:
		return "business"
	}
}

// Error is the error code structure which contains error code type and error message.
type Error struct {
	Type int
	Code int32
	Msg  string
	Desc string

	cause error      // internal error, form the error chain.
	stack stackTrace // call stack, if the error chain already has a stack, it will not be set.
}

func (e *Error) GRPCStatus() *status.Status {
	return status.FromProto(&spb.Status{
		Code:    e.Code,
		Message: e.Msg,
	})
}

// Error implements the error interface and returns the error description.
func (e *Error) Error() string {
	if e == nil {
		return Success
	}

	if e.cause != nil {
		return fmt.Sprintf("type:%s, code:%d, msg:%s, caused by %s",
			typeDesc(e.Type), e.Code, e.Msg, e.cause.Error())
	}
	return fmt.Sprintf("type:%s, code:%d, msg:%s", typeDesc(e.Type), e.Code, e.Msg)
}

// Format implements the fmt.Formatter interface.
func (e *Error) Format(s fmt.State, verb rune) {
	var stackTrace stackTrace
	defer func() {
		if stackTrace != nil {
			stackTrace.Format(s, verb)
		}
	}()
	switch verb {
	case 'v':
		if s.Flag('+') {
			_, _ = fmt.Fprintf(s, "type:%s, code:%d, msg:%s", typeDesc(e.Type), e.Code, e.Msg)
			if e.stack != nil {
				stackTrace = e.stack
			}
			if e.Unwrap() != nil {
				_, _ = fmt.Fprintf(s, "\nCause by %+v", e.Unwrap())
			}
			return
		}
		fallthrough
	case 's':
		_, _ = io.WriteString(s, e.Error())
	case 'q':
		_, _ = fmt.Fprintf(s, "%q", e.Error())
	default:
		_, _ = fmt.Fprintf(s, "%%!%c(errs.Error=%s)", verb, e.Error())
	}
}

// Unwrap support Go 1.13+ error chains.
func (e *Error) Unwrap() error { return e.cause }

// IsTimeout checks whether this error is a timeout error with error type typ.
func (e *Error) IsTimeout(typ int) bool {
	return e.Type == typ &&
		(e.Code == int32(codes.DeadlineExceeded) ||
			e.Code == int32(codes.Canceled))
}

// ErrCode permits any integer defined in https://go.dev/ref/spec#Numeric_types
type ErrCode interface {
	~uint8 | ~uint16 | ~uint32 | ~uint64 | ~int8 | ~int16 | ~int32 | ~int64 | ~uint | ~int | ~uintptr
}

// errorCodeMap 存储错误码和对应的错误信息
var errorCodeMap = make(map[int32]string)

// initDefaultErrorCodes 初始化默认的错误码
func initDefaultErrorCodes() {
	// Google gRPC standard error codes
	errorCodeMap[int32(codes.Canceled)] = "Operation canceled"
	errorCodeMap[int32(codes.Unknown)] = "Unknown error"
	errorCodeMap[int32(codes.InvalidArgument)] = "Invalid argument"
	errorCodeMap[int32(codes.DeadlineExceeded)] = "Operation timeout"
	errorCodeMap[int32(codes.NotFound)] = "Resource not found"
	errorCodeMap[int32(codes.AlreadyExists)] = "Resource already exists"
	errorCodeMap[int32(codes.PermissionDenied)] = "Permission denied"
	errorCodeMap[int32(codes.ResourceExhausted)] = "Resource exhausted"
	errorCodeMap[int32(codes.FailedPrecondition)] = "Failed precondition"
	errorCodeMap[int32(codes.Aborted)] = "Operation aborted"
	errorCodeMap[int32(codes.OutOfRange)] = "Out of range"
	errorCodeMap[int32(codes.Unimplemented)] = "Not implemented"
	errorCodeMap[int32(codes.Internal)] = "Internal error"
	errorCodeMap[int32(codes.Unavailable)] = "Service unavailable"
	errorCodeMap[int32(codes.DataLoss)] = "Data loss"
	errorCodeMap[int32(codes.Unauthenticated)] = "Unauthenticated"
}

func init() {
	initDefaultErrorCodes()
}

// Register 批量注册错误码和对应的错误信息
func Register(codes map[int32]string) {
	for code, msg := range codes {
		errorCodeMap[code] = msg
	}
}

func New[T ErrCode](code T) error {
	if msg, ok := errorCodeMap[int32(code)]; ok {
		return Newm(code, msg)
	}
	return Newm(code, errorCodeMap[int32(codes.Unknown)])
}

// New creates a business error with the given code and message.
func Newm[T ErrCode](code T, msg string) error {
	// 如果提供了错误信息，使用提供的错误信息
	// 否则尝试从错误码映射表中获取
	if msg == "" {
		if mappedMsg, ok := errorCodeMap[int32(code)]; ok {
			msg = mappedMsg
		}
	}

	err := &Error{
		Type: ErrorTypeBusiness,
		Code: int32(code),
		Msg:  msg,
	}
	if traceable {
		err.stack = callers()
	}
	return err
}

// Newf creates a business error with the given code and formatted message.
func Newf[T ErrCode](code T, format string, params ...interface{}) error {
	msg := fmt.Sprintf(format, params...)
	err := &Error{
		Type: ErrorTypeBusiness,
		Code: int32(code),
		Msg:  msg,
	}
	if traceable {
		err.stack = callers()
	}
	return err
}

// Wrap creates a new error contains input error.
func Wrap[T ErrCode](err error, code T, msg string) error {
	if err == nil {
		return nil
	}
	wrapErr := &Error{
		Type:  ErrorTypeBusiness,
		Code:  int32(code),
		Msg:   msg,
		cause: err,
	}
	var e *Error
	if traceable && !errors.As(err, &e) {
		wrapErr.stack = callers()
	}
	return wrapErr
}

// Wrapf the same as Wrap, msg supports format strings.
func Wrapf[T ErrCode](err error, code T, format string, params ...interface{}) error {
	if err == nil {
		return nil
	}
	msg := fmt.Sprintf(format, params...)
	wrapErr := &Error{
		Type:  ErrorTypeBusiness,
		Code:  int32(code),
		Msg:   msg,
		cause: err,
	}
	var e *Error
	if traceable && !errors.As(err, &e) {
		wrapErr.stack = callers()
	}
	return wrapErr
}

// NewFrameError creates a framework error.
func NewFrameError[T ErrCode](code T, msg string) error {
	err := &Error{
		Type: ErrorTypeFramework,
		Code: int32(code),
		Msg:  msg,
		Desc: "rpc",
	}
	if traceable {
		err.stack = callers()
	}
	return err
}

// WrapFrameError the same as Wrap, except type is ErrorTypeFramework
func WrapFrameError[T ErrCode](err error, code T, msg string) error {
	if err == nil {
		return nil
	}
	wrapErr := &Error{
		Type:  ErrorTypeFramework,
		Code:  int32(code),
		Msg:   msg,
		Desc:  "rpc",
		cause: err,
	}
	var e *Error
	if traceable && !errors.As(err, &e) {
		wrapErr.stack = callers()
	}
	return wrapErr
}

// Code gets the error code through error.
func Code(e error) int32 {
	if e == nil {
		return int32(codes.OK)
	}

	err, ok := e.(*Error)
	if !ok && !errors.As(e, &err) {
		return int32(codes.Unknown)
	}
	if err == nil {
		return int32(codes.OK)
	}
	return err.Code
}

// Msg gets error msg through error.
func Msg(e error) string {
	if e == nil {
		return Success
	}
	err, ok := e.(*Error)
	if !ok && !errors.As(e, &err) {
		return e.Error()
	}
	if err == (*Error)(nil) {
		return Success
	}
	if err.Unwrap() != nil {
		return err.Error()
	}
	return err.Msg
}

// Type gets the error type through error.
func Type(e error) int {
	if e == nil {
		return ErrorTypeUnknown
	}

	err, ok := e.(*Error)
	if !ok && !errors.As(e, &err) {
		return ErrorTypeUnknown
	}
	if err == nil {
		return ErrorTypeUnknown
	}
	return err.Type
}
