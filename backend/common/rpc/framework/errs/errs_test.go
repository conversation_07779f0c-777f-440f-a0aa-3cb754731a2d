package errs_test

import (
	"bufio"
	"bytes"
	"errors"
	"fmt"
	"os"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc/codes"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
)

func TestErrs(t *testing.T) {
	var err *errs.Error
	str := err.Error()
	assert.Contains(t, str, "success")

	e := errs.Newm(111, "inner fail")
	assert.NotNil(t, e)

	assert.EqualValues(t, 111, errs.Code(e))
	assert.Equal(t, "inner fail", errs.Msg(e))

	err, ok := e.(*errs.Error)
	assert.Equal(t, true, ok)
	assert.NotNil(t, err)
	assert.Equal(t, errs.ErrorTypeBusiness, err.Type)

	str = err.Error()
	assert.Contains(t, str, "business")

	e = errs.NewFrameError(111, "inner fail")
	assert.NotNil(t, e)

	assert.EqualValues(t, 111, errs.Code(e))
	assert.Equal(t, "inner fail", errs.Msg(e))

	err, ok = e.(*errs.Error)
	assert.Equal(t, true, ok)
	assert.NotNil(t, err)
	assert.Equal(t, errs.ErrorTypeFramework, err.Type)

	str = err.Error()
	assert.Contains(t, str, "framework")

	assert.EqualValues(t, 0, errs.Code(nil))
	assert.Equal(t, "success", errs.Msg(nil))

	assert.EqualValues(t, 0, errs.Code((*errs.Error)(nil)))
	assert.Equal(t, "success", errs.Msg((*errs.Error)(nil)))

	e = errors.New("unknown error")
	assert.Equal(t, int32(codes.Unknown), errs.Code(e))
	assert.Equal(t, "unknown error", errs.Msg(e))

	err.Type = errs.ErrorTypeCalleeFramework
	assert.Contains(t, err.Error(), "type:callee framework")
}

func TestNonEmptyStringOnEmptyMsg(t *testing.T) {
	e := errs.New(codes.Internal)
	require.Contains(t, e.Error(), "code:")
	require.Contains(t, e.Error(), "type:")
}

func TestErrsFormat(t *testing.T) {
	err := errs.Newm(10000, "test error")

	s := fmt.Sprintf("%s", err)
	assert.Equal(t, "type:business, code:10000, msg:test error", s)

	s = fmt.Sprintf("%q", err)
	assert.Equal(t, `"type:business, code:10000, msg:test error"`, s)

	s = fmt.Sprintf("%v", err)
	assert.Equal(t, "type:business, code:10000, msg:test error", s)

	s = fmt.Sprintf("%d", err)
	assert.Equal(t, "%!d(errs.Error=type:business, code:10000, msg:test error)", s)
}

func TestNewFrameError(t *testing.T) {
	ok := true
	errs.SetTraceable(ok)
	e := errs.NewFrameError(111, "inner fail")
	assert.NotNil(t, e)
}

func TestWrapFrameError(t *testing.T) {
	ok := true
	errs.SetTraceable(ok)
	e := errs.WrapFrameError(errs.New(123), 456, "wrap frame error")
	assert.NotNil(t, e)
	e = errs.WrapFrameError(nil, 456, "wrap frame error")
	assert.Nil(t, e)
}

func TestTraceError(t *testing.T) {
	errs.SetTraceable(true)

	err := parent()
	assert.NotNil(t, err)

	s := fmt.Sprintf("%+v", err)
	br := bufio.NewReader(strings.NewReader(s))

	line, isPrefix, err := br.ReadLine()
	assert.Equal(t, "type:business, code:111, msg:inner fail", string(line))
	assert.Equal(t, isPrefix, false)
	assert.Nil(t, err)

	line, isPrefix, err = br.ReadLine()
	assert.Contains(t, string(line), "grandson")
	assert.Equal(t, isPrefix, false)
	assert.Nil(t, err)

	_, _, _ = br.ReadLine()
	line, isPrefix, err = br.ReadLine()
	assert.Contains(t, string(line), "child")
	assert.Equal(t, isPrefix, false)
	assert.Nil(t, err)

	_, _, _ = br.ReadLine()
	line, isPrefix, err = br.ReadLine()
	assert.Contains(t, string(line), "parent")
	assert.Equal(t, isPrefix, false)
	assert.Nil(t, err)
}

func TestTraceErrorSetStackSkip(t *testing.T) {
	errs.SetTraceable(true)
	errs.SetStackSkip(4)

	err := func() error {
		return func() error {
			return newMyErr(11, "TestTraceErrorSetStackSkip error")
		}()
	}()
	assert.NotNil(t, err)

	s := fmt.Sprintf("%+v", err)
	br := bufio.NewReader(strings.NewReader(s))

	line, isPrefix, err := br.ReadLine()
	assert.Equal(t, "type:business, code:11, msg:TestTraceErrorSetStackSkip error", string(line))
	assert.Equal(t, isPrefix, false)
	assert.Nil(t, err)

	line, isPrefix, err = br.ReadLine()
	t.Log(string(line))
	assert.Contains(t, string(line), "TestTraceErrorSetStackSkip")
	assert.Equal(t, isPrefix, false)
	assert.Nil(t, err)
}

func newMyErr(code int, msg string) error {
	return errs.Newm(code, msg)
}

func TestSetTraceableWithContent(t *testing.T) {
	errs.SetTraceableWithContent("child")

	err := parent()
	assert.NotNil(t, err)

	s := fmt.Sprintf("%+v", err)
	br := bufio.NewReader(strings.NewReader(s))
	line, isPrefix, err := br.ReadLine()
	assert.Equal(t, "type:business, code:111, msg:inner fail", string(line))
	assert.Equal(t, isPrefix, false)
	assert.Nil(t, err)

	line, isPrefix, err = br.ReadLine()
	assert.Contains(t, string(line), "child")
	assert.Equal(t, isPrefix, false)
	assert.Nil(t, err)
}

func TestErrorChain(t *testing.T) {
	var e error = errs.Wrap(os.ErrDeadlineExceeded, int32(codes.DeadlineExceeded), "just wrap")
	require.Contains(t, errs.Msg(e), os.ErrDeadlineExceeded.Error())
	e = fmt.Errorf("%w", e)
	require.Equal(t, int32(codes.DeadlineExceeded), errs.Code(e))
	require.True(t, errors.Is(e, os.ErrDeadlineExceeded))
	require.Contains(t, e.Error(), os.ErrDeadlineExceeded.Error())
}

func TestWrap(t *testing.T) {
	err := parent()
	assert.NotNil(t, err)

	err = errs.Wrap(err, 222, "wrap err")
	assert.NotNil(t, err)

	s := fmt.Sprintf("%v", err)
	assert.Contains(t, s, "type:business, code:222, msg:wrap err")
	s = fmt.Sprintf("%s", err)
	assert.Contains(t, s, "type:business, code:222, msg:wrap err")

	s = fmt.Sprintf("%+v", err)
	br := bufio.NewReader(strings.NewReader(s))
	line, isPrefix, err := br.ReadLine()
	assert.Equal(t, "type:business, code:222, msg:wrap err", string(line))
	assert.Equal(t, isPrefix, false)
	assert.Nil(t, err)

	line, isPrefix, err = br.ReadLine()
	assert.Equal(t, "Cause by type:business, code:111, msg:inner fail", string(line))
	assert.Equal(t, isPrefix, false)
	assert.Nil(t, err)
}

func TestWrapf(t *testing.T) {
	err := parent()
	assert.NotNil(t, err)

	err = errs.Wrapf(err, 222, "wrap %v", "err")
	assert.NotNil(t, err)

	s := fmt.Sprintf("%+v", err)
	br := bufio.NewReader(strings.NewReader(s))
	line, isPrefix, err := br.ReadLine()
	assert.Equal(t, "type:business, code:222, msg:wrap err", string(line))
	assert.Equal(t, isPrefix, false)
	assert.Nil(t, err)

	line, isPrefix, err = br.ReadLine()
	assert.Equal(t, "Cause by type:business, code:111, msg:inner fail", string(line))
	assert.Equal(t, isPrefix, false)
	assert.Nil(t, err)
}

func TestWrapSetTraceable(t *testing.T) {
	// reset
	errs.SetStackSkip(3)
	errs.SetTraceableWithContent("")

	err := parent()
	assert.NotNil(t, err)

	err = errs.Wrap(err, 222, "wrap err")
	assert.NotNil(t, err)

	s := fmt.Sprintf("%+v", err)
	br := bufio.NewReader(strings.NewReader(s))
	line, isPrefix, err := br.ReadLine()
	assert.Equal(t, "type:business, code:222, msg:wrap err", string(line))
	assert.Equal(t, isPrefix, false)
	assert.Nil(t, err)

	line, isPrefix, err = br.ReadLine()
	assert.Equal(t, "Cause by type:business, code:111, msg:inner fail", string(line))
	assert.Equal(t, isPrefix, false)
	assert.Nil(t, err)

	line, isPrefix, err = br.ReadLine()
	assert.Contains(t, string(line), "grandson")
	assert.Equal(t, isPrefix, false)
	assert.Nil(t, err)
}

func TestIsTimeout(t *testing.T) {
	require.True(t, (&errs.Error{
		Type: errs.ErrorTypeFramework,
		Code: int32(codes.DeadlineExceeded),
	}).IsTimeout(errs.ErrorTypeFramework))
	require.True(t, (&errs.Error{
		Type: errs.ErrorTypeCalleeFramework,
		Code: int32(codes.DeadlineExceeded),
	}).IsTimeout(errs.ErrorTypeCalleeFramework))
	require.False(t, (&errs.Error{
		Type: errs.ErrorTypeBusiness,
		Code: int32(codes.DeadlineExceeded),
	}).IsTimeout(errs.ErrorTypeFramework))
	require.True(t, (&errs.Error{
		Type: errs.ErrorTypeFramework,
		Code: int32(codes.Canceled),
	}).IsTimeout(errs.ErrorTypeFramework))
	require.False(t, (&errs.Error{
		Type: errs.ErrorTypeFramework,
		Code: int32(codes.Internal),
	}).IsTimeout(errs.ErrorTypeFramework))
}

func TestErrorFormatPrint(t *testing.T) {
	errs.SetTraceable(false)
	defer errs.SetTraceable(true)
	err := errs.Newm(9999, "")
	var buf bytes.Buffer
	fmt.Fprintf(&buf, "%+v", err)
	require.Equal(t, "type:business, code:9999, msg:", buf.String())
}

func TestNestErrors(t *testing.T) {
	errs.SetTraceable(false)
	defer errs.SetTraceable(true)
	const (
		code = 101
		msg  = "test error"
	)
	require.Equal(t, int32(code), errs.Code(&testError{Err: errs.Newm(code, msg)}))
	require.Equal(t, msg, errs.Msg(&testError{Err: errs.Newm(code, msg)}))
}

type testError struct {
	Err error
}

func (te *testError) Error() string {
	return te.Err.Error()
}

func (te *testError) Unwrap() error {
	return te.Err
}

//go:noinline
func parent() error {
	if err := child(); err != nil {
		return err
	}
	return nil
}

//go:noinline
func child() error {
	if err := grandson(); err != nil {
		return err
	}
	return nil
}

//go:noinline
func grandson() error {
	return errs.Newm(111, "inner fail")
}

func TestErrorCodeRegistration(t *testing.T) {
	// Test default error codes
	err := errs.New(codes.NotFound)
	assert.Equal(t, "Resource not found", errs.Msg(err))

	err = errs.New(codes.Internal)
	assert.Equal(t, "Internal error", errs.Msg(err))

	// Test registering new error codes
	customCodes := map[int32]string{
		2001: "Custom error 1",
		2002: "Custom error 2",
	}
	errs.Register(customCodes)

	// Test registered error codes
	err = errs.New(2001)
	assert.Equal(t, "Custom error 1", errs.Msg(err))

	err = errs.New(2002)
	assert.Equal(t, "Custom error 2", errs.Msg(err))

	// Test overwriting existing error codes
	overwriteCodes := map[int32]string{
		2001: "Overwritten error 1",
	}
	errs.Register(overwriteCodes)

	err = errs.New(2001)
	assert.Equal(t, "Overwritten error 1", errs.Msg(err))

	// Test unknown error code
	err = errs.New(9999)
	assert.Equal(t, "Unknown error", errs.Msg(err))
}

func TestErrorCodeRegistrationWithEmptyMap(t *testing.T) {
	// Test registering empty map
	errs.Register(map[int32]string{})

	// Should not affect existing error codes
	err := errs.New(codes.NotFound)
	assert.Equal(t, "Resource not found", errs.Msg(err))
}

func TestErrorCodeRegistrationWithNilMap(t *testing.T) {
	// Test registering nil map
	errs.Register(nil)

	// Should not affect existing error codes
	err := errs.New(codes.NotFound)
	assert.Equal(t, "Resource not found", errs.Msg(err))
}

func TestErrorCodeRegistrationConcurrent(t *testing.T) {
	// Test concurrent registration
	done := make(chan bool)

	go func() {
		errs.Register(map[int32]string{3001: "Concurrent error 1"})
		done <- true
	}()

	go func() {
		errs.Register(map[int32]string{3002: "Concurrent error 2"})
		done <- true
	}()

	<-done
	<-done

	// Verify both error codes were registered
	err := errs.New(3001)
	assert.Equal(t, "Concurrent error 1", errs.Msg(err))

	err = errs.New(3002)
	assert.Equal(t, "Concurrent error 2", errs.Msg(err))
}
