package errs_test

import (
	"fmt"
	"strconv"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
)

func TestSetTraceable(t *testing.T) {
	// 测试默认值
	assert.False(t, errs.IsTraceable())

	// 测试设置
	errs.SetTraceable(true)
	assert.True(t, errs.IsTraceable())

	// 测试重置
	errs.SetTraceable(false)
	assert.False(t, errs.IsTraceable())
}

func TestSetStackSkip(t *testing.T) {
	// 测试默认值
	assert.Equal(t, 3, errs.GetStackSkip())

	// 测试设置
	errs.SetStackSkip(4)
	assert.Equal(t, 4, errs.GetStackSkip())

	// 测试重置
	errs.SetStackSkip(3)
	assert.Equal(t, 3, errs.GetStackSkip())
}

func TestStackTraceFormat(t *testing.T) {
	errs.SetTraceable(true)
	defer errs.SetTraceable(false)

	// 生成一个带堆栈的错误
	err := generateErrorWithStack()
	require.NotNil(t, err)

	// 测试 %v 格式
	s := fmt.Sprintf("%v", err)
	assert.Contains(t, s, "type:business, code:100, msg:test error")

	// 测试 %+v 格式（带堆栈信息）
	s = fmt.Sprintf("%+v", err)
	assert.Contains(t, s, "type:business, code:100, msg:test error")
	assert.Contains(t, s, "TestStackTraceFormat")
	assert.Contains(t, s, "generateErrorWithStack")
}

func TestStackTraceWithContentFilter(t *testing.T) {
	errs.SetTraceableWithContent("stack_test.go")
	defer errs.SetTraceableWithContent("")

	// 生成一个带堆栈的错误
	err := generateErrorWithStack()
	require.NotNil(t, err)

	// 测试堆栈信息过滤
	s := fmt.Sprintf("%+v", err)
	lines := strings.Split(s, "\n")

	// 验证堆栈信息只包含测试文件
	for _, line := range lines {
		if strings.Contains(line, ".go") {
			assert.Contains(t, line, "stack_test.go")
		}
	}
}

func TestFrameFormat(t *testing.T) {
	errs.SetTraceable(true)
	defer errs.SetTraceable(false)

	// 生成一个带堆栈的错误
	err := generateErrorWithStack()
	require.NotNil(t, err)

	// 测试不同的格式化选项
	s := fmt.Sprintf("%+v", err)
	t.Logf("Stack trace:\n%s", s)
	lines := strings.Split(s, "\n")
	require.Greater(t, len(lines), 1)

	// 验证堆栈信息的格式
	for i := 1; i < len(lines); i += 2 { // 每次处理两行
		if i+1 >= len(lines) {
			break
		}

		// 第一行：函数名
		funcLine := lines[i]
		assert.NotEmpty(t, funcLine, "function name should not be empty")

		// 第二行：文件路径和行号
		fileLine := lines[i+1]
		assert.True(t, strings.HasPrefix(fileLine, "\t"), "file line should be indented")

		// 验证行号格式（:数字）
		parts := strings.Split(fileLine, ":")
		require.Equal(t, 2, len(parts), "file line should contain file path and line number")
		assert.NotEmpty(t, parts[0], "file path should not be empty")
		assert.NotEmpty(t, parts[1], "line number should not be empty")
		_, err := strconv.Atoi(strings.TrimSpace(parts[1]))
		assert.NoError(t, err, "line number should be a valid integer")
	}
}

func TestCallers(t *testing.T) {
	errs.SetTraceable(true)
	defer errs.SetTraceable(false)

	// 测试不同层级的调用
	err := func() error {
		return func() error {
			return func() error {
				return errs.Newm(100, "test error")
			}()
		}()
	}()
	require.NotNil(t, err)

	s := fmt.Sprintf("%+v", err)
	assert.Contains(t, s, "TestCallers")
}

// 辅助函数：生成带堆栈的错误
func generateErrorWithStack() error {
	return errs.Newm(100, "test error")
}
