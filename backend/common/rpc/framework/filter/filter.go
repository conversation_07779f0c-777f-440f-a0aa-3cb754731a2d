package filter

import (
	"context"
	"sync"
)

type ServerHandleFunc func(ctx context.Context, req interface{}) (interface{}, error)

type ClientHandleFunc func(ctx context.Context, req, rsp interface{}) error

type ServerFilter func(ctx context.Context, req interface{}, next ServerHandleFunc) (interface{}, error)

type ClientFilter func(ctx context.Context, req, rsp interface{}, next ClientHandleFunc) error

func NoopServerFilter(ctx context.Context, req interface{}, next ServerHandleFunc) (interface{}, error) {
	return next(ctx, req)
}

type Server<PERSON>hain []ServerFilter

func (c ServerChain) Filter(ctx context.Context, req interface{}, next ServerHandleFunc) (interface{}, error) {
	nextF := func(ctx context.Context, req interface{}) (rsp interface{}, err error) {
		rsp, err = next(ctx, req)
		return rsp, err
	}

	for i := len(c) - 1; i >= 0; i-- {
		curHandleFunc, curFilter, _ := nextF, c[i], i
		nextF = func(ctx context.Context, req interface{}) (interface{}, error) {
			rsp, err := curFilter(ctx, req, curHandleFunc)
			return rsp, err
		}
	}
	return nextF(ctx, req)
}

type ClientChain []ClientFilter

func (c ClientChain) Filter(ctx context.Context, req, rsp interface{}, next ClientHandleFunc) error {
	nextF := func(ctx context.Context, req, rsp interface{}) error {
		return next(ctx, req, rsp)
	}

	for i := len(c) - 1; i >= 0; i-- {
		curHandleFunc, curFilter := nextF, c[i]
		nextF = func(ctx context.Context, req interface{}, rsp interface{}) error {
			return curFilter(ctx, req, rsp, curHandleFunc)
		}
	}

	return nextF(ctx, req, rsp)
}

var (
	lock          = sync.RWMutex{}
	serverFilters = make(map[string]ServerFilter)
	clientFilters = make(map[string]ClientFilter)
)

// Register registers server/client filters by name.
func Register(name string, s ServerFilter, c ClientFilter) {
	lock.Lock()
	defer lock.Unlock()
	serverFilters[name] = s
	clientFilters[name] = c
}

// GetServer gets the ServerFilter by name.
func GetServer(name string) ServerFilter {
	lock.RLock()
	f := serverFilters[name]
	lock.RUnlock()
	return f
}

func GetClient(name string) ClientFilter {
	lock.RLock()
	f := clientFilters[name]
	lock.RUnlock()
	return f
}
