package framework

import (
	"context"
	"net"
	"sync"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
)

func BackgroundContext() context.Context {
	cfg := GlobalConfig()
	ctx, msg := codec.WithNewMessage(context.Background())
	msg.WithNamespace(cfg.Global.Namespace)
	msg.WithEnvName(cfg.Global.EnvName)
	if len(cfg.Server.Service) > 0 {
		msg.WithCalleeServiceName(cfg.Server.Service[0].Name)
	} else {
		msg.WithCalleeApp(cfg.Server.App)
		msg.WithCalleeServer(cfg.Server.Server)
	}
	return ctx
}

// nicIP defines the parameters used to record the ip address (ipv4 & ipv6) of the nic.
type nicIP struct {
	nic  string
	ipv4 []string
	ipv6 []string
}

// netInterfaceIP maintains the nic name to nicIP mapping.
type netInterfaceIP struct {
	once sync.Once
	ips  map[string]*nicIP
}

// enumAllIP returns the nic name to nicIP mapping.
func (p *netInterfaceIP) enumAllIP() map[string]*nicIP {
	p.once.Do(func() {
		p.ips = make(map[string]*nicIP)
		interfaces, err := net.Interfaces()
		if err != nil {
			return
		}
		for _, i := range interfaces {
			p.addInterface(i)
		}
	})
	return p.ips
}

func (p *netInterfaceIP) addInterface(i net.Interface) {
	addrs, err := i.Addrs()
	if err != nil {
		return
	}
	for _, addr := range addrs {
		ipNet, ok := addr.(*net.IPNet)
		if !ok {
			continue
		}
		if ipNet.IP.To4() != nil {
			p.addIPv4(i.Name, ipNet.IP.String())
		} else if ipNet.IP.To16() != nil {
			p.addIPv6(i.Name, ipNet.IP.String())
		}
	}
}

// addIPv4 append ipv4 address
func (p *netInterfaceIP) addIPv4(nic string, ip4 string) {
	ips := p.getNicIP(nic)
	ips.ipv4 = append(ips.ipv4, ip4)
}

// addIPv6 append ipv6 address
func (p *netInterfaceIP) addIPv6(nic string, ip6 string) {
	ips := p.getNicIP(nic)
	ips.ipv6 = append(ips.ipv6, ip6)
}

// getNicIP returns nicIP by nic name.
func (p *netInterfaceIP) getNicIP(nic string) *nicIP {
	if _, ok := p.ips[nic]; !ok {
		p.ips[nic] = &nicIP{nic: nic}
	}
	return p.ips[nic]
}

// getIPByNic returns ip address by nic name.
// If the ipv4 addr is not empty, it will be returned.
// Otherwise, the ipv6 addr will be returned.
func (p *netInterfaceIP) getIPByNic(nic string) string {
	p.enumAllIP()
	if len(p.ips) == 0 {
		return ""
	}
	if _, ok := p.ips[nic]; !ok {
		return ""
	}
	ip := p.ips[nic]
	if len(ip.ipv4) > 0 {
		return ip.ipv4[0]
	}
	if len(ip.ipv6) > 0 {
		return ip.ipv6[0]
	}
	return ""
}

// localIP records the local nic name->nicIP mapping.
var localIP = &netInterfaceIP{}

// getIP returns ip addr by nic name.
func getIP(nic string) string {
	ip := localIP.getIPByNic(nic)
	return ip
}

func deduplicate(a, b []string) []string {
	r := make([]string, 0, len(a)+len(b))
	m := make(map[string]bool)
	for _, s := range append(a, b...) {
		if _, ok := m[s]; !ok {
			m[s] = true
			r = append(r, s)
		}
	}
	return r
}

func SetMetaData(ctx context.Context, key string, val []string) {
	msg := codec.Message(ctx)
	if len(msg.ServerMetaData()) > 0 {
		msg.ServerMetaData()[key] = val
		return
	}
	md := make(map[string][]string)
	md[key] = val
	msg.WithServerMetaData(md)
}
