package http

import (
	"errors"
	"net/url"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
)

func init() {
	codec.RegisterSerializer(codec.SerializationTypeQuery, NewQuerySerialization(tag))
}

// NewQuerySerialization initializes the query serialized object.
func NewQuerySerialization(tag string) codec.Serializer {
	return &QuerySerialization{
		tag: tag,
	}
}

// QuerySerialization packages kv structure of the http get request.
type QuerySerialization struct {
	tag string
}

// Unmarshal unpacks kv structure.
func (s *QuerySerialization) Unmarshal(in []byte, body interface{}) error {
	values, err := url.ParseQuery(string(in))
	if err != nil {
		return err
	}
	return unmarshalValues(s.tag, values, body)
}

// Marshal packages kv structure.
func (s *QuerySerialization) Marshal(body interface{}) ([]byte, error) {
	jsonSerializer := codec.GetSerializer(codec.SerializationTypeJSON)
	if jsonSerializer == nil {
		return nil, errors.New("empty json serializer")
	}
	return jsonSerializer.<PERSON>(body)
}
