package http

import (
	"bytes"
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/http/httptrace"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"golang.org/x/net/http2"
	"google.golang.org/grpc/codes"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http/tls"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport"
)

func init() {
	st := NewServerTransport(func() *http.Server { return &http.Server{} })
	DefaultServerTransport = st
	DefaultHTTP2ServerTransport = st
	// Server transport (protocol file service).
	transport.RegisterServerTransport("http", st)
	transport.RegisterServerTransport("http2", st)
	// Server transport (no protocol file service).
	transport.RegisterServerTransport("http_no_protocol", st)
	transport.RegisterServerTransport("http2_no_protocol", st)
	// Client transport.
	transport.RegisterClientTransport("http", DefaultClientTransport)
	transport.RegisterClientTransport("http2", DefaultHTTP2ClientTransport)
}

// DefaultServerTransport is the default server http transport.
var DefaultServerTransport transport.ServerTransport

// DefaultHTTP2ServerTransport is the default server http2 transport.
var DefaultHTTP2ServerTransport transport.ServerTransport

// ServerTransport is the http transport layer.
type ServerTransport struct {
	newServer func() *http.Server
}

// NewServerTransport creates a new ServerTransport which implement transport.ServerTransport.
// The parameter newStdHttpServer is used to create the underlying http.Server when ListenAndServe, and that server
// is modified by opts of this function and ListenAndServe.
func NewServerTransport(
	newStdHttpServer func() *http.Server,
	opts ...OptServerTransport,
) *ServerTransport {
	st := ServerTransport{newServer: newStdHttpServer}
	for _, opt := range opts {
		opt(&st)
	}
	return &st
}

// ListenAndServe handles configuration.
func (t *ServerTransport) ListenAndServe(ctx context.Context, opt ...transport.ListenServeOption) error {
	opts := &transport.ListenServeOptions{
		Network: "tcp",
	}
	for _, o := range opt {
		o(opts)
	}
	if opts.Handler == nil {
		return errors.New("http server transport handler empty")
	}
	return t.listenAndServeHTTP(ctx, opts)
}

var emptyBuf []byte

func (t *ServerTransport) listenAndServeHTTP(ctx context.Context, opts *transport.ListenServeOptions) error {
	// All rpc-go http server transport only register this http.Handler.
	serveFunc := func(w http.ResponseWriter, r *http.Request) {
		h := &Header{Request: r, Response: w}
		ctx := WithHeader(r.Context(), h)

		// Generates new empty general message structure data and save it to ctx.
		ctx, msg := codec.WithNewMessage(ctx)
		defer codec.PutBackMessage(msg)
		// The old request must be replaced to ensure that the context is embedded.
		h.Request = r.WithContext(ctx)
		defer func() {
			// Fix issues/778
			if r.MultipartForm == nil {
				r.MultipartForm = h.Request.MultipartForm
			}
		}()

		// Records LocalAddr and RemoteAddr to Context.
		localAddr, ok := h.Request.Context().Value(http.LocalAddrContextKey).(net.Addr)
		if ok {
			msg.WithLocalAddr(localAddr)
		}
		raddr, _ := net.ResolveTCPAddr("tcp", h.Request.RemoteAddr)
		msg.WithRemoteAddr(raddr)
		_, err := opts.Handler.Handle(ctx, emptyBuf)
		if err != nil {
			log.Errorf("http server transport handle fail:%v", err)
			if errors.Is(err, ErrEncodeMissingHeader) {
				w.WriteHeader(http.StatusInternalServerError)
				_, _ = w.Write([]byte(fmt.Sprintf("http server handle error: %+v", err)))
			}
			return
		}
	}

	s, err := t.newHTTPServer(serveFunc, opts)
	if err != nil {
		return err
	}

	if err := t.serve(ctx, s, opts); err != nil {
		return err
	}
	return nil
}

func (t *ServerTransport) serve(ctx context.Context, s *http.Server, opts *transport.ListenServeOptions) error {
	ln := opts.Listener
	if ln == nil {
		var err error
		ln, err = t.getListener(opts.Network, s.Addr)
		if err != nil {
			return fmt.Errorf("http server transport get listener err: %w", err)
		}
	}

	go func() {
		_ = s.Serve(tcpKeepAliveListener{ln.(*net.TCPListener)})
	}()

	go func() {
		<-opts.StopListening
		ln.Close()
	}()
	return nil
}

func (t *ServerTransport) getListener(network, addr string) (net.Listener, error) {
	var ln net.Listener
	ln, err := net.Listen(network, addr)
	if err != nil {
		return nil, fmt.Errorf("http listen error:%v", err)
	}
	return ln, nil
}

// newHTTPServer creates http server.
func (t *ServerTransport) newHTTPServer(
	serveFunc func(w http.ResponseWriter, r *http.Request),
	opts *transport.ListenServeOptions,
) (*http.Server, error) {
	s := t.newServer()
	s.Addr = opts.Address
	s.Handler = http.HandlerFunc(serveFunc)
	return s, nil
}

// tcpKeepAliveListener sets TCP keep-alive timeouts on accepted
// connections. It's used by ListenAndServe and ListenAndServeTLS so
// dead TCP connections (e.g. closing laptop mid-download) eventually
// go away.
type tcpKeepAliveListener struct {
	*net.TCPListener
}

// Accept accepts new request.
func (ln tcpKeepAliveListener) Accept() (net.Conn, error) {
	tc, err := ln.AcceptTCP()
	if err != nil {
		return nil, err
	}
	_ = tc.SetKeepAlive(true)
	_ = tc.SetKeepAlivePeriod(3 * time.Minute)
	return tc, nil
}

// ClientTransport client side http transport.
type ClientTransport struct {
	http.Client // http client, exposed variables, allow user to customize settings.
	opts        *transport.ClientTransportOptions
	tlsClients  map[string]*http.Client // Different certificate file use different TLS client.
	tlsLock     sync.RWMutex
	http2Only   bool
}

// DefaultClientTransport default client http transport.
var DefaultClientTransport = NewClientTransport(false)

// DefaultHTTP2ClientTransport default client http2 transport.
var DefaultHTTP2ClientTransport = NewClientTransport(true)

// NewClientTransport creates http transport.
func NewClientTransport(http2Only bool, opt ...transport.ClientTransportOption) transport.ClientTransport {
	opts := &transport.ClientTransportOptions{}

	// Write func options to field opts.
	for _, o := range opt {
		o(opts)
	}
	return &ClientTransport{
		opts: opts,
		Client: http.Client{
			Transport: StdHTTPTransport,
		},
		tlsClients: make(map[string]*http.Client),
		http2Only:  http2Only,
	}
}

func (ct *ClientTransport) getRequest(reqHeader *ClientReqHeader,
	reqBody []byte, msg codec.Msg, opts *transport.RoundTripOptions) (*http.Request, error) {
	req, err := ct.newRequest(reqHeader, reqBody, msg, opts)
	if err != nil {
		return nil, err
	}

	if reqHeader.Header != nil {
		req.Header = make(http.Header)
		for h, val := range reqHeader.Header {
			req.Header[h] = val
		}
	}
	if len(reqHeader.Host) != 0 {
		req.Host = reqHeader.Host
	}
	req.Header.Set(rpcCaller, msg.CallerServiceName())
	req.Header.Set(rpcCallee, msg.CalleeServiceName())
	req.Header.Set(rpcTimeout, strconv.Itoa(int(msg.RequestTimeout()/time.Millisecond)))
	if t, ok := compressTypeContentEncoding[msg.CompressType()]; ok {
		req.Header.Set("Content-Encoding", t)
	}
	if msg.SerializationType() != codec.SerializationTypeNoop {
		if len(req.Header.Get("Content-Type")) == 0 {
			req.Header.Set("Content-Type",
				serializationTypeContentType[msg.SerializationType()])
		}
	}
	if err := ct.setTransInfo(msg, req); err != nil {
		return nil, err
	}
	// reqHeader 用于承载请求上报信息，因此在生成最终的 http request 之后需要回写回去，方便进行上报
	reqHeader.Request = req
	return req, nil
}

func (ct *ClientTransport) setTransInfo(msg codec.Msg, req *http.Request) error {
	var m map[string][]string
	if md := msg.ClientMetaData(); len(md) > 0 {
		m = make(map[string][]string, len(md))
		for k, v := range md {
			for _, s := range v {
				m[k] = append(m[k], ct.encodeBytes([]byte(s)))
			}
		}
	}

	if len(m) > 0 {
		val, err := codec.Marshal(codec.SerializationTypeJSON, m)
		if err != nil {
			return errs.NewFrameError(codes.InvalidArgument, "http client json marshal metadata fail: "+err.Error())
		}
		req.Header.Set(rpcTransInfo, string(val))
	}
	return nil
}

func (ct *ClientTransport) newRequest(reqHeader *ClientReqHeader,
	reqBody []byte, msg codec.Msg, opts *transport.RoundTripOptions) (*http.Request, error) {
	if reqHeader.Request != nil {
		return reqHeader.Request, nil
	}
	scheme := reqHeader.Schema
	if scheme == "" {
		if len(opts.CACertFile) > 0 || strings.HasSuffix(opts.Address, ":443") {
			scheme = "https"
		} else {
			scheme = "http"
		}
	}

	body := reqHeader.ReqBody
	if body == nil {
		body = bytes.NewReader(reqBody)
	}

	request, err := http.NewRequest(
		reqHeader.Method,
		fmt.Sprintf("%s://%s%s", scheme, opts.Address, msg.ClientRPCName()),
		body)
	if err != nil {
		return nil, errs.NewFrameError(codes.Internal, "http client transport NewRequest: "+err.Error())
	}
	return request, nil
}

func (ct *ClientTransport) encodeBytes(in []byte) string {
	return base64.StdEncoding.EncodeToString(in)
}

func (ct *ClientTransport) encodeString(in string) string {
	return base64.StdEncoding.EncodeToString([]byte(in))
}

// RoundTrip sends and receives http packets, put http response into ctx,
// no need to return rspBuf here.
func (ct *ClientTransport) RoundTrip(
	ctx context.Context,
	reqBody []byte,
	callOpts ...transport.RoundTripOption,
) (rspBody []byte, err error) {
	msg := codec.Message(ctx)
	reqHeader, ok := msg.ClientReqHead().(*ClientReqHeader)
	if !ok {
		return nil, errs.NewFrameError(codes.Internal,
			"http client transport: ReqHead should be type of *http.ClientReqHeader")
	}
	rspHeader, ok := msg.ClientRspHead().(*ClientRspHeader)
	if !ok {
		return nil, errs.NewFrameError(codes.Internal,
			"http client transport: RspHead should be type of *http.ClientRspHeader")
	}

	var opts transport.RoundTripOptions
	for _, o := range callOpts {
		o(&opts)
	}

	// Sets reqHeader.
	req, err := ct.getRequest(reqHeader, reqBody, msg, &opts)
	if err != nil {
		return nil, err
	}
	trace := &httptrace.ClientTrace{
		GotConn: func(info httptrace.GotConnInfo) {
			msg.WithRemoteAddr(info.Conn.RemoteAddr())
		},
	}
	reqCtx := ctx
	cancel := context.CancelFunc(func() {})
	if rspHeader.ManualReadBody {
		// In the scenario of Manual Read body, the lifecycle of rsp body is different
		// from that of invoke ctx, and is independently controlled by body.Close().
		// Therefore, the timeout/cancel function in the original context needs to be replaced.
		reqCtx = context.WithoutCancel(reqCtx)
		if deadline, ok := ctx.Deadline(); ok {
			reqCtx, cancel = context.WithDeadline(reqCtx, deadline)
		}
	}
	defer func() {
		if err != nil {
			cancel()
		}
	}()
	request := req.WithContext(httptrace.WithClientTrace(reqCtx, trace))

	client, err := ct.getStdHTTPClient(opts.CACertFile, opts.TLSCertFile, opts.TLSKeyFile, opts.TLSServerName)
	if err != nil {
		return nil, err
	}

	rspHeader.Response, err = client.Do(request)
	if err != nil {
		if e, ok := err.(*url.Error); ok {
			if e.Timeout() {
				return nil, errs.NewFrameError(codes.DeadlineExceeded,
					"http client transport RoundTrip timeout: "+err.Error())
			}
		}
		if errors.Is(ctx.Err(), context.Canceled) {
			return nil, errs.NewFrameError(codes.Canceled,
				"http client transport RoundTrip canceled: "+err.Error())
		}
		return nil, errs.NewFrameError(codes.DataLoss,
			"http client transport RoundTrip: "+err.Error())
	}
	decorateWithCancel(rspHeader, cancel)
	return emptyBuf, nil
}

func decorateWithCancel(rspHeader *ClientRspHeader, cancel context.CancelFunc) {
	// Quoted from: https://github.com/golang/go/blob/go1.21.4/src/net/http/response.go#L69
	//
	// "As of Go 1.12, the Body will also implement io.Writer on a successful "101 Switching Protocols" response,
	// as used by WebSockets and HTTP/2's "h2c" mode."
	//
	// Therefore, we require an extra check to ensure io.Writer's conformity,
	// which will then expose the corresponding method.
	//
	// It's important to note that an embedded body may not be capable of exposing all the attached interfaces.
	// Consequently, we perform an explicit interface assertion here.
	if body, ok := rspHeader.Response.Body.(io.ReadWriteCloser); ok {
		rspHeader.Response.Body = &writableResponseBodyWithCancel{ReadWriteCloser: body, cancel: cancel}
	} else {
		rspHeader.Response.Body = &responseBodyWithCancel{ReadCloser: rspHeader.Response.Body, cancel: cancel}
	}
}

// writableResponseBodyWithCancel implements io.ReadWriteCloser.
// It wraps response body and cancel function.
type writableResponseBodyWithCancel struct {
	io.ReadWriteCloser
	cancel context.CancelFunc
}

func (b *writableResponseBodyWithCancel) Close() error {
	b.cancel()
	return b.ReadWriteCloser.Close()
}

// responseBodyWithCancel implements io.ReadCloser.
// It wraps response body and cancel function.
type responseBodyWithCancel struct {
	io.ReadCloser
	cancel context.CancelFunc
}

func (b *responseBodyWithCancel) Close() error {
	b.cancel()
	return b.ReadCloser.Close()
}

func (ct *ClientTransport) getStdHTTPClient(caFile, certFile,
	keyFile, serverName string) (*http.Client, error) {
	if len(caFile) == 0 { // HTTP requests share one client.
		return &ct.Client, nil
	}

	cacheKey := fmt.Sprintf("%s-%s-%s", caFile, certFile, serverName)
	ct.tlsLock.RLock()
	cli, ok := ct.tlsClients[cacheKey]
	ct.tlsLock.RUnlock()
	if ok {
		return cli, nil
	}

	ct.tlsLock.Lock()
	defer ct.tlsLock.Unlock()
	cli, ok = ct.tlsClients[cacheKey]
	if ok {
		return cli, nil
	}

	conf, err := tls.GetClientConfig(serverName, caFile, certFile, keyFile)
	if err != nil {
		return nil, err
	}
	client := &http.Client{
		CheckRedirect: ct.Client.CheckRedirect,
		Timeout:       ct.Client.Timeout,
	}
	if ct.http2Only {
		client.Transport = &http2.Transport{
			TLSClientConfig: conf,
		}
	} else {
		tr := StdHTTPTransport.Clone()
		tr.TLSClientConfig = conf
		client.Transport = tr
	}
	ct.tlsClients[cacheKey] = client
	return client, nil
}

// StdHTTPTransport all RoundTripper object used by http and https.
var StdHTTPTransport = &http.Transport{
	Proxy: http.ProxyFromEnvironment,
	DialContext: (&net.Dialer{
		Timeout:   30 * time.Second,
		KeepAlive: 30 * time.Second,
		DualStack: true,
	}).DialContext,
	ForceAttemptHTTP2:     true,
	IdleConnTimeout:       50 * time.Second,
	TLSHandshakeTimeout:   10 * time.Second,
	MaxIdleConnsPerHost:   100,
	DisableCompression:    true,
	ExpectContinueTimeout: time.Second,
}
