load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "http",
    srcs = [
        "client.go",
        "codec.go",
        "serialization_form.go",
        "serialization_form_data.go",
        "serialization_query.go",
        "service_desc.go",
        "transport.go",
        "transport_options.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/framework/http",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/client",
        "//backend/common/rpc/framework/codec",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/http/tls",
        "//backend/common/rpc/framework/log",
        "//backend/common/rpc/framework/server",
        "//backend/common/rpc/framework/transport",
        "@com_github_go_playground_form_v4//:form",
        "@com_github_mitchellh_mapstructure//:mapstructure",
        "@org_golang_google_grpc//codes",
        "@org_golang_x_net//http2",
    ],
)
