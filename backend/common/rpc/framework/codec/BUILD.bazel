load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "codec",
    srcs = [
        "codec.go",
        "compress.go",
        "compress_gzip.go",
        "framer_builder.go",
        "message.go",
        "message_impl.go",
        "serialization.go",
        "serialization_json.go",
        "serialization_proto.go",
        "serialization_protojson.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/errs",
        "@com_github_bytedance_sonic//:sonic",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
    ],
)
