package codec

import (
	"errors"
)

type Compressor interface {
	Compress(in []byte) (out []byte, err error)
	Decompress(in []byte) (out []byte, err error)
}

const (
	CompressTypeNoop = iota
	CompressTypeGzip
)

var compressors = make(map[int]Compressor)

func RegisterCompressor(compressType int, s Compressor) {
	compressors[compressType] = s
}

func GetCompressor(compressType int) Compressor {
	return compressors[compressType]
}

func Compress(compressorType int, in []byte) ([]byte, error) {
	if compressorType == CompressTypeNoop {
		return in, nil
	}
	if len(in) == 0 {
		return nil, nil
	}
	compressor := GetCompressor(compressorType)
	if compressor == nil {
		return nil, errors.New("compressor not registered")
	}
	return compressor.Compress(in)
}

func Decompress(compressorType int, in []byte) ([]byte, error) {
	if compressorType == CompressTypeNoop {
		return in, nil
	}
	if len(in) == 0 {
		return nil, nil
	}
	compressor := GetCompressor(compressorType)
	if compressor == nil {
		return nil, errors.New("compressor not registered")
	}
	return compressor.Decompress(in)
}
