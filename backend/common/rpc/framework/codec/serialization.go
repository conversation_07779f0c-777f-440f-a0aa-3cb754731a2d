package codec

import (
	"errors"
)

type Serializer interface {
	Unmarshal(in []byte, body interface{}) error
	Marshal(body interface{}) (out []byte, err error)
}

const (
	SerializationTypePB         = 0
	SerializationTypeJSON       = 2
	SerializationTypeFlatBuffer = 3
	SerializationTypeNoop       = 4
	SerializationTypeXML        = 5
	SerializationTypeTextXML    = 6

	SerializationTypeForm     = 129
	SerializationTypeQuery    = 130
	SerializationTypeFormData = 131
)

var serializers = make(map[int]Serializer)

func RegisterSerializer(serializationType int, s Serializer) {
	serializers[serializationType] = s
}

func GetSerializer(serializationType int) Serializer {
	return serializers[serializationType]
}

func Unmarshal(serializationType int, in []byte, body interface{}) error {
	if body == nil {
		return nil
	}
	if len(in) == 0 {
		return nil
	}

	s := GetSerializer(serializationType)
	if s == nil {
		return errors.New("serializer not registered")
	}
	return s.Unmarshal(in, body)
}

func Marshal(serializationType int, body interface{}) ([]byte, error) {
	if body == nil {
		return nil, nil
	}

	s := GetSerializer(serializationType)
	if s == nil {
		return nil, errors.New("serializer not registered")
	}
	return s.Marshal(body)
}
