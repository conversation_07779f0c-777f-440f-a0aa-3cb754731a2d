package codec

import (
	"context"
	"net"
	"sync"
	"time"
)

type MetaData map[string][]string

var msgPool = sync.Pool{
	New: func() interface{} {
		return &msg{}
	},
}

func (m MetaData) Clone() MetaData {
	if m == nil {
		return nil
	}
	md := MetaData{}
	for k, v := range m {
		md[k] = v
	}
	return md
}

type CommonMeta map[interface{}]interface{}

func (c CommonMeta) Clone() CommonMeta {
	if c == nil {
		return nil
	}
	cm := CommonMeta{}
	for k, v := range c {
		cm[k] = v
	}
	return cm
}

type Msg interface {
	Context() context.Context
	WithRemoteAddr(addr net.Addr)
	WithLocalAddr(addr net.Addr)
	RemoteAddr() net.Addr
	LocalAddr() net.Addr
	WithRequestTimeout(time.Duration)
	RequestTimeout() time.Duration
	WithSerializationType(int)
	SerializationType() int
	WithCompressType(int)
	CompressType() int
	WithServerRPCName(string)
	WithClientRPCName(string)
	ServerRPCName() string
	ClientRPCName() string
	WithNamespace(string)
	Namespace() string
	WithEnvName(string)
	EnvName() string
	WithCallerServiceName(string)
	WithCalleeServiceName(string)
	WithCallerApp(string)
	WithCallerServer(string)
	WithCallerService(string)
	WithCallerMethod(string)
	WithCalleeApp(string)
	WithCalleeServer(string)
	WithCalleeService(string)
	WithCalleeMethod(string)
	CallerServiceName() string
	CalleeServiceName() string
	CallerApp() string
	CallerServer() string
	CallerService() string
	CallerMethod() string
	CalleeApp() string
	CalleeServer() string
	CalleeService() string
	CalleeMethod() string
	WithServerMetaData(MetaData)
	ServerMetaData() MetaData
	WithFrameHead(interface{})
	FrameHead() interface{}
	WithServerReqHead(interface{})
	ServerReqHead() interface{}
	WithServerRspHead(interface{})
	ServerRspHead() interface{}
	WithServerRspErr(error)
	ServerRspErr() error
	WithClientMetaData(MetaData)
	ClientMetaData() MetaData
	WithClientReqHead(interface{})
	ClientReqHead() interface{}
	WithClientRspErr(error)
	ClientRspErr() error
	WithClientRspHead(interface{})
	ClientRspHead() interface{}
	WithLogger(interface{})
	Logger() interface{}
	WithRequestID(uint32)
	RequestID() uint32
	WithCommonMeta(CommonMeta)
	CommonMeta() CommonMeta
}

type ContextKey string

const (
	ContextKeyMessage    = ContextKey("RPC_MESSAGE")
	ServiceSectionLength = 4
)
