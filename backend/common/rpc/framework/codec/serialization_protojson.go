package codec

import (
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

func init() {
	RegisterSerializer(SerializationTypeJSON, &ProtoJSONSerialization{})
}

var Marshaler = protojson.MarshalOptions{}

var Unmarshaler = protojson.UnmarshalOptions{DiscardUnknown: true}

type ProtoJSONSerialization struct{}

func (_ *ProtoJSONSerialization) Name() string {
	return "json"
}

func (s *ProtoJSONSerialization) Unmarshal(in []byte, body interface{}) error {
	input, ok := body.(proto.Message)
	if !ok {
		return JSONAPI.Unmarshal(in, body)
	}
	return Unmarshaler.Unmarshal(in, input)
}

func (s *ProtoJSONSerialization) Marshal(body interface{}) ([]byte, error) {
	input, ok := body.(proto.Message)
	if !ok {
		return JSONAPI.Marshal(body)
	}
	return Marshaler.Marshal(input)
}
