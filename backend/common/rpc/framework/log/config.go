package log

const (
	OutputConsole = "console"
	OutputFile    = "file"
)

type Config []OutputConfig

type OutputConfig struct {
	// Writer is the output of log, such as console or file.
	Writer string `yaml:"writer"`

	// Formatter is the format of log, such as console or json.
	Formatter string `yaml:"formatter"`

	Level string `yaml:"level"`

	// CallerSkip controls the nesting depth of log function.
	CallerSkip int `yaml:"caller_skip"`
}
