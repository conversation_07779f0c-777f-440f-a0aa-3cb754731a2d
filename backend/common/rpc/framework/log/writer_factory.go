package log

import (
	"errors"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/plugin"
)

var (
	writers = make(map[string]plugin.Factory)
)

func RegisterWriter(name string, writer plugin.Factory) {
	writers[name] = writer
}

func GetWriter(name string) plugin.Factory {
	return writers[name]
}

type ConsoleWriterFactory struct{}

func (f *ConsoleWriterFactory) Type() string {
	return pluginType
}

func (f *ConsoleWriterFactory) Setup(_ string, dec plugin.Decoder) error {
	if dec == nil {
		return errors.New("console writer decoder is nil")
	}
	decoder, ok := dec.(*Decoder)
	if !ok {
		return errors.New("console writer decoder is not *Decoder")
	}
	cfg := &OutputConfig{}
	if err := decoder.Decode(&cfg); err != nil {
		return err
	}
	decoder.Core, decoder.ZapLevel = newConsoleCore(cfg)
	return nil
}
