package log

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
)

var traceEnabled = false

func EnableTrace() {
	traceEnabled = true
}

// SetLevel sets log level for different output which may be "0", "1" or "2".
func SetLevel(output string, level Level) {
	GetDefaultLogger().SetLevel(output, level)
}

// GetLevel gets log level for different output.
func GetLevel(output string) Level {
	return GetDefaultLogger().GetLevel(output)
}

// With adds user defined fields to Logger. Field support multiple values.
func With(fields ...Field) Logger {
	if ol, ok := GetDefaultLogger().(OptionLogger); ok {
		return ol.WithOptions(WithAdditionalCallerSkip(-1)).With(fields...)
	}
	return GetDefaultLogger().With(fields...)
}

// WithContext add user defined fields to the Logger of context. Fields support multiple values.
func WithContext(ctx context.Context, fields ...Field) Logger {
	logger, ok := codec.Message(ctx).Logger().(Logger)
	if !ok {
		return With(fields...)
	}
	if ol, ok := logger.(OptionLogger); ok {
		return ol.WithOptions(WithAdditionalCallerSkip(-1)).With(fields...)
	}
	return logger.With(fields...)
}

// Trace logs to TRACE log. Arguments are handled in the manner of fmt.Println.
func Trace(args ...interface{}) {
	if traceEnabled {
		GetDefaultLogger().Trace(args...)
	}
}

// Tracef logs to TRACE log. Arguments are handled in the manner of fmt.Printf.
func Tracef(format string, args ...interface{}) {
	if traceEnabled {
		GetDefaultLogger().Tracef(format, args...)
	}
}

// TraceContext logs to TRACE log. Arguments are handled in the manner of fmt.Println.
func TraceContext(ctx context.Context, args ...interface{}) {
	if !traceEnabled {
		return
	}
	if l, ok := codec.Message(ctx).Logger().(Logger); ok {
		l.Trace(args...)
		return
	}
	GetDefaultLogger().Trace(args...)
}

// TraceContextf logs to TRACE log. Arguments are handled in the manner of fmt.Printf.
func TraceContextf(ctx context.Context, format string, args ...interface{}) {
	if !traceEnabled {
		return
	}
	if l, ok := codec.Message(ctx).Logger().(Logger); ok {
		l.Tracef(format, args...)
		return
	}
	GetDefaultLogger().Tracef(format, args...)
}

// Debug logs to DEBUG log. Arguments are handled in the manner of fmt.Println.
func Debug(args ...interface{}) {
	GetDefaultLogger().Debug(args...)
}

// Debugf logs to DEBUG log. Arguments are handled in the manner of fmt.Printf.
func Debugf(format string, args ...interface{}) {
	GetDefaultLogger().Debugf(format, args...)
}

// Info logs to INFO log. Arguments are handled in the manner of fmt.Println.
func Info(args ...interface{}) {
	GetDefaultLogger().Info(args...)
}

// Infof logs to INFO log. Arguments are handled in the manner of fmt.Printf.
func Infof(format string, args ...interface{}) {
	GetDefaultLogger().Infof(format, args...)
}

// Warn logs to WARNING log. Arguments are handled in the manner of fmt.Println.
func Warn(args ...interface{}) {
	GetDefaultLogger().Warn(args...)
}

// Warnf logs to WARNING log. Arguments are handled in the manner of fmt.Printf.
func Warnf(format string, args ...interface{}) {
	GetDefaultLogger().Warnf(format, args...)
}

// Error logs to ERROR log. Arguments are handled in the manner of fmt.Println.
func Error(args ...interface{}) {
	GetDefaultLogger().Error(args...)
}

// Errorf logs to ERROR log. Arguments are handled in the manner of fmt.Printf.
func Errorf(format string, args ...interface{}) {
	GetDefaultLogger().Errorf(format, args...)
}

// Fatal logs to ERROR log. Arguments are handled in the manner of fmt.Println.
// All Fatal logs will exit by calling os.Exit(1).
// Implementations may also call os.Exit() with a non-zero exit code.
func Fatal(args ...interface{}) {
	GetDefaultLogger().Fatal(args...)
}

// Fatalf logs to ERROR log. Arguments are handled in the manner of fmt.Printf.
func Fatalf(format string, args ...interface{}) {
	GetDefaultLogger().Fatalf(format, args...)
}

// DebugContext logs to DEBUG log. Arguments are handled in the manner of fmt.Println.
func DebugContext(ctx context.Context, args ...interface{}) {
	if l, ok := codec.Message(ctx).Logger().(Logger); ok {
		l.Debug(args...)
		return
	}
	GetDefaultLogger().Debug(args...)
}

// DebugContextf logs to DEBUG log. Arguments are handled in the manner of fmt.Printf.
func DebugContextf(ctx context.Context, format string, args ...interface{}) {
	if l, ok := codec.Message(ctx).Logger().(Logger); ok {
		l.Debugf(format, args...)
		return
	}
	GetDefaultLogger().Debugf(format, args...)
}

// InfoContext logs to INFO log. Arguments are handled in the manner of fmt.Println.
func InfoContext(ctx context.Context, args ...interface{}) {
	if l, ok := codec.Message(ctx).Logger().(Logger); ok {
		l.Info(args...)
		return
	}
	GetDefaultLogger().Info(args...)
}

// InfoContextf logs to INFO log. Arguments are handled in the manner of fmt.Printf.
func InfoContextf(ctx context.Context, format string, args ...interface{}) {
	if l, ok := codec.Message(ctx).Logger().(Logger); ok {
		l.Infof(format, args...)
		return
	}
	GetDefaultLogger().Infof(format, args...)
}

// WarnContext logs to WARNING log. Arguments are handled in the manner of fmt.Println.
func WarnContext(ctx context.Context, args ...interface{}) {
	if l, ok := codec.Message(ctx).Logger().(Logger); ok {
		l.Warn(args...)
		return
	}
	GetDefaultLogger().Warn(args...)
}

// WarnContextf logs to WARNING log. Arguments are handled in the manner of fmt.Printf.
func WarnContextf(ctx context.Context, format string, args ...interface{}) {
	if l, ok := codec.Message(ctx).Logger().(Logger); ok {
		l.Warnf(format, args...)
		return
	}
	GetDefaultLogger().Warnf(format, args...)

}

// ErrorContext logs to ERROR log. Arguments are handled in the manner of fmt.Println.
func ErrorContext(ctx context.Context, args ...interface{}) {
	if l, ok := codec.Message(ctx).Logger().(Logger); ok {
		l.Error(args...)
		return
	}
	GetDefaultLogger().Error(args...)
}

// ErrorContextf logs to ERROR log. Arguments are handled in the manner of fmt.Printf.
func ErrorContextf(ctx context.Context, format string, args ...interface{}) {
	if l, ok := codec.Message(ctx).Logger().(Logger); ok {
		l.Errorf(format, args...)
		return
	}
	GetDefaultLogger().Errorf(format, args...)
}

// FatalContext logs to ERROR log. Arguments are handled in the manner of fmt.Println.
// All Fatal logs will exit by calling os.Exit(1).
// Implementations may also call os.Exit() with a non-zero exit code.
func FatalContext(ctx context.Context, args ...interface{}) {
	if l, ok := codec.Message(ctx).Logger().(Logger); ok {
		l.Fatal(args...)
		return
	}
	GetDefaultLogger().Fatal(args...)
}

// FatalContextf logs to ERROR log. Arguments are handled in the manner of fmt.Printf.
func FatalContextf(ctx context.Context, format string, args ...interface{}) {
	if l, ok := codec.Message(ctx).Logger().(Logger); ok {
		l.Fatalf(format, args...)
		return
	}
	GetDefaultLogger().Fatalf(format, args...)
}

// WithContextFields sets some user defined data to logs, such as uid, imei, etc.
// Fields must be paired.
// If ctx has already set a Msg, this function returns that ctx, otherwise, it returns a new one.
func WithContextFields(ctx context.Context, fields ...Field) context.Context {
	ctx, msg := codec.EnsureMessage(ctx)
	logger, ok := msg.Logger().(Logger)
	if ok && logger != nil {
		logger = logger.With(fields...)
	} else {
		logger = GetDefaultLogger().With(fields...)
	}

	msg.WithLogger(logger)
	return ctx
}
