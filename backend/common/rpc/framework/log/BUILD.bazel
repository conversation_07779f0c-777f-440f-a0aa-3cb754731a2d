load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "log",
    srcs = [
        "config.go",
        "log.go",
        "logger.go",
        "logger_factory.go",
        "options.go",
        "writer_factory.go",
        "zaplogger.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/common/rpc/framework/log",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/codec",
        "//backend/common/rpc/framework/plugin",
        "@org_uber_go_zap//:zap",
        "@org_uber_go_zap//zapcore",
    ],
)
