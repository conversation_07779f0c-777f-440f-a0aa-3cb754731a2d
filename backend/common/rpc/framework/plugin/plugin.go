package plugin

var plugins = make(map[string]map[string]Factory)

type Factory interface {
	Type() string
	Setup(name string, dec Decoder) error
}

type Decoder interface {
	Decode(cfg interface{}) error
}

func Register(name string, f Factory) {
	factories, ok := plugins[f.Type()]
	if !ok {
		factories = make(map[string]Factory)
		plugins[f.Type()] = factories
	}
	factories[name] = f
}

func Get(typ, name string) Factory {
	return plugins[typ][name]
}
