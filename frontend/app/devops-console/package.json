{"name": "amis-react-starter", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-free": "^6.1.1", "@react-oauth/google": "^0.12.2", "@types/node": "^16.11.11", "amis": "6.10.0", "amis-ui": "6.10.0", "axios": "^0.26.1", "copy-to-clipboard": "^3.3.1", "monofile-utilities": "^5.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^4.4.3"}, "scripts": {"vite": "vite", "vite-build": "vite build", "dev": "vite", "start": "vite", "build": "vite build --mode production", "preview": "vite preview", "analyze": "vite build --mode production --analyze"}, "devDependencies": {"@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^1.1.0", "prettier": "^2.2.1", "vite": "^2.6.14"}}