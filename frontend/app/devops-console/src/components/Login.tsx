import { useState } from 'react';
import { CredentialResponse, GoogleLogin, GoogleOAuthProvider } from '@react-oauth/google';
import { Spinner, toast } from 'amis';
import { parse } from 'monofile-utilities/lib/query-string';

import { getEnvConfig } from '../globals';
import { invokeApi } from '../utils/api';

export interface LoginProps {
    className?: string;
}

export const Login = () => {
    const query = parse<{
        from?: string;
        code?: string;
        state?: string;
        redirect?: string;
    }>(location.search.substring(1));
    const redirect = query.redirect || '/';

    const [loading, setLoading] = useState(true);

    const handleGoogleSuccess = async (response: CredentialResponse) => {
        if (!response.credential) {
            handleGoogleError();
            return;
        }
        setLoading(true);
        try {
            await invokeApi('/devops/auth/google/login', {
                credential: response.credential,
            });
            location.href = redirect;
        } catch {
            setLoading(false);
        }
    };

    const handleGoogleError = (...args: any[]) => {
        toast.error('Unknown error occurred.' + args.join(', '));
    };

    return (
        <>
            <div style={{ display: 'flex', justifyContent: 'center', paddingTop: '100px' }}>
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '150px',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                    }}
                >
                    <GoogleOAuthProvider
                        clientId={getEnvConfig().googleClientId}
                        onScriptLoadSuccess={() => setLoading(false)}
                        onScriptLoadError={() => {
                            toast.error('Load GSI script failed, please refresh the page or log in with MoeGo SSO.');
                            setLoading(false);
                        }}
                    >
                        <GoogleLogin
                            hosted_domain="moego.pet"
                            ux_mode="popup"
                            onSuccess={handleGoogleSuccess}
                            onError={handleGoogleError}
                        />
                    </GoogleOAuthProvider>
                </div>
            </div>
            <Spinner overlay show={loading} />
        </>
    );
};

export default Login;
