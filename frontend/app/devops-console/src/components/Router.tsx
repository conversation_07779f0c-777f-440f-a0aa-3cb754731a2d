import { ComponentType, memo, Suspense } from 'react';
import { Spinner } from 'amis';

export interface RouteConfig {
  path: string;
  redirect?: string;
  component?: ComponentType;
}

export interface RouterProps {
  routes: RouteConfig[];
}

export const Router = memo<RouterProps>(({ routes }) => {
  const path = location.pathname;
  for (const route of routes) {
    if (path.startsWith(route.path)) {
      if (route.redirect) {
        location.href = route.redirect;
        return null;
      }
      if (!route.component) {
        throw new Error(`route component is required, path: ${route.path}`);
      }
      return (
        <Suspense fallback={<Spinner overlay className="m-t-lg" size="lg" />}>
          <route.component />
        </Suspense>
      );
    }
  }
  return null;
});
