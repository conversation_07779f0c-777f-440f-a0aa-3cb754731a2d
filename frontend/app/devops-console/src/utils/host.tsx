export const changeHost = (url: string): string => {

  // 本地开发模式, 不需要修改 host
  if (process.env.NODE_ENV === 'development') {
    // console.log('本地开发模式');
    return url;
  }

  // 如果 url 已经包含 host, 则不需要修改 host
  if (url.startsWith('http')) {
    return url;
  }

  // 获取当前 host
  var apiHost = 'platform-tools.t2.moego.dev'
  const webHost = window.location.host;
  // 判断 host 的开头是否正则匹配 (.*)-grey-
  const isGrey = webHost.match(/^(.*)-grey-.*$/);
  if (isGrey) {
    // apiHost 添加前缀
    apiHost = isGrey[1] + '-grey-' + apiHost;
  }
  
  if (url.startsWith('/')) {
    return 'https://' + apiHost + url;
  }
  return 'https://' + apiHost + '/' + url;
};
