import { toast } from 'amis';

export async function invokeApi<K extends string>(
  key: K,
  input: any,
): Promise<any> {
  let res: Response;
  let body: any;
  try {
    res = await fetch(key, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(input),
    });
    body = await res.json();
  } catch (e: any) {
    toast.error(`Load ${key} failed: ${e.message || e + ''}`);
    throw e;
  }
  if (res.status === 200) {
    return body;
  } else {
    toast.error(`${body.code}: ${body.message || body.data}`);
  }
  throw {
    ...res,
    body,
  };
}
