{"type": "page", "title": "ai-studio-模版测试", "body": [{"id": "u:058073818157", "type": "form", "title": "表单", "body": [{"type": "input-text", "id": "u:d9cd6a4532b5", "name": "name", "label": "<strong>模版名称</strong>：请取个好记的名字，不可重复", "row": 0, "value": "", "colSize": "1"}, {"type": "input-text", "id": "u:d9cd6a4532b0", "name": "geminiKey", "label": "<strong><PERSON><PERSON><PERSON></strong>：请去aistudio.google.com获取", "row": 1, "value": "AIzaSyBFjCEU8GBqUJCLtNZ4gfg4aXn2MdisML", "colSize": "1"}, {"type": "select", "label": "<strong>模型</strong>", "name": "model", "options": [{"label": "gemini-2.0-flash(token多，速度快)", "value": "gemini-2.0-flash"}, {"label": "gemini-2.5-pro(token少，速度慢，效果好)", "value": "gemini-2.5-pro"}, {"label": "gemini-2.5-flash(token少，速度慢，效果好)", "value": "gemini-2.5-flash"}, {"label": "moego-gemini-2.5-flash", "value": "moego-gemini-2.5-flash"}], "row": 2, "colSize": "1", "id": "u:3d996369ee44", "multiple": false, "value": "gemini-2.0-flash"}, {"name": "prompt", "label": "<strong>Prompt：</strong>最好为英文，中文有时会任务失败，这是Prompt的示例", "row": 3, "type": "textarea", "id": "u:b2dc1a24e180", "value": "This is a multi-step task, please complete it in order:\n1. Please first get all Jira Tasks or Stories, condition: assignee is {{jira-username}}, all projects, up to 10 items, recent time.\n2. Optimize and outputs(Do not update any jira tickets) the summaries of these tickets from the user perspective, outputs, and outcomes. A good example is: As a user role, I hope to do outputs and achieve outcomes.\n3. Final output format: jira-key:xxxx \\n origin-summary: xxx \\n Optimized summary: using Chinese", "minRows": 3, "maxRows": 20, "colSize": "1"}, {"name": "mcps", "label": "<strong>mcp列表：</strong>选取这个Prompt会使用的MCP，<span style='color:red'>不要多选。</span>如果你选择了对应的MCP，则必须在下面填写相关的环境变量", "row": 4, "type": "checkboxes", "id": "u:deb8e73b5e64", "colSize": "1", "value": "mcp-atlassian,datadog,mcp-slack,unix_timestamps_mcp", "multiple": true, "options": [{"label": "mcp-atlassian", "value": "mcp-atlassian"}, {"label": "datadog", "value": "datadog"}, {"label": "mcp-slack", "value": "mcp-slack"}, {"label": "unix_timestamps_mcp", "value": "unix_timestamps_mcp"}], "checkAll": false, "joinValues": true}, {"type": "textarea", "label": "<strong>环境变量：</strong>可以通过{{key}}在prompt中使用，也可以用来给MCP提供参数. 这里只是示例，请使用自己的Token", "name": "envs", "row": 5, "colSize": "1", "id": "u:fd1fe1dd88fd", "minRows": 3, "maxRows": 20, "value": "datadog-api-key=2c2dae1ad5d7952998d15af901a7f60d\ndatadog-app-key=****************************************\n\njira-username=zih<PERSON>@moego.pet\njira-token=ATATT3xFfGF02xghAYSPSRN6DQMAjhoacPIPP_vp2MoDFQevHEhTBdq1t4SgmE9yoIim6pZdC2CFqzpgq-mA9kQHuLipoblU2cy_yGmmNkG7K2bpI-UgfPQaoJPfLB50NX4DgmSiTkSQImTJ56y8QO48339kHl50vCV6ygMQQBWXwQaCo53vJks=E96C7984\n\nslack-bot-token=xoxb-xxxx-xxxx-xxxxxxx"}, {"type": "code", "colSize": "1", "id": "u:25f6c89176c4", "language": "html", "value": "调试消息：展示所有执行过程，供调试prompts使用. 正式任务中最后一条消息将作为结果发送到slack\n${dialogues|join:'\n\n消息:\n'}", "row": 6}], "labelAlign": "top", "mode": "flex", "actions": [{"type": "button", "label": "测试运行", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "u:058073818157"}]}}, "level": "primary", "id": "u:a3dd75d89041"}], "reload": "", "resetAfterSubmit": false, "api": {"url": "/moego.bff/devops/ai-studio/requestAiStudioMcpClient", "method": "post", "dataType": "json", "data": {"geminiKey": "${geminiKey}", "prompt": "${prompt}", "mcps": "${mcps}", "envs": "${envs}", "model": "${model}"}}, "feat": "Insert", "dsType": "api", "persistData": false}], "id": "u:4745f068d191", "asideResizor": false, "pullRefresh": {"disabled": true}, "aside": [], "toolbar": [{"type": "button-group", "buttons": [{"type": "button", "label": "发布配置", "id": "u:a4e7a5adddac", "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "outputVar": "responseResult", "actionType": "ajax", "options": {}, "api": {"url": "/moego.bff/devops/ai-studio/createAiStudioTemplate", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "dataType": "json", "data": {"geminiKey": "${GETRENDERERDATA(\"u:058073818157\", \"geminiKey\")}", "prompt": "${GETRENDERERDATA(\"u:058073818157\", \"prompt\")}", "mcps": "${GETRENDERERDATA(\"u:058073818157\", \"mcps\")}", "envs": "${GETRENDERERDATA(\"u:058073818157\", \"envs\")}", "model": "${GETRENDERERDATA(\"u:058073818157\", \"model\")}", "name": "${GETRENDERERDATA(\"u:058073818157\", \"name\")}"}}}, {"ignoreError": false, "actionType": "url", "args": {"url": "/aistudio-template-details", "params": {"template_id": "${id}"}, "blank": false}}]}}, "confirmText": "确认测试好了？点击后会将模版存储到DB，并跳转到模板编辑页"}], "id": "u:d705bee87338", "vertical": false}]}