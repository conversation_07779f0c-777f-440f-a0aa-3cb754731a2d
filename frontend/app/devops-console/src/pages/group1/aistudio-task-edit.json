{"type": "page", "title": "ai-studio-修改任务", "initApi": {"method": "post", "url": "/moego.bff/devops/ai-studio/getAiStudioTask", "data": {"id": "${task_id}"}}, "body": [{"id": "u:c4284fcac038", "type": "form", "title": "表单", "mode": "flex", "labelAlign": "top", "dsType": "api", "feat": "Insert", "body": [{"type": "input-text", "label": "任务ID", "name": "id", "id": "u:09bded46b16b", "row": 0, "readOnly": true}, {"name": "task", "label": "task名", "row": 1, "type": "input-text", "id": "u:54b3867fb872"}, {"type": "input-text", "row": 2, "id": "u:54b3867fb877", "name": "a<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "templateId", "row": 3, "type": "static", "id": "u:f1cdc42c8805", "tpl": "模版ID: <a href='/aistudio-template-details?template_id=${templateId}' target='_blank' >${templateId}</a> "}, {"name": "envs", "label": "环境变量", "row": 4, "type": "textarea", "id": "u:4f273e55716b", "minRows": 3, "maxRows": 20}, {"row": 5, "type": "input-text", "id": "u:66a6736ea22e", "colSize": "1", "label": "<strong>频道 ID：</strong>如C08MXLJELBZ，结果将被推送到这个Slack中，请添加PlatformsApp应用", "name": "imChannel"}, {"row": 6, "type": "select", "colSize": "1", "label": "<strong>定时运行：</strong>可选", "name": "spec", "id": "u:99e8b63deea9", "options": [{"label": "不定时运行", "value": ""}, {"label": "每天早上10点", "value": "SpecDaily10AM"}, {"label": "每天下午6点", "value": "SpecDaily6PM"}, {"label": "每30分钟", "value": "SpecEvery30Min"}, {"label": "每5分钟", "value": "SpecEvery5Min"}, {"label": "每天15点", "value": "SpecDaily3PM"}, {"label": "周五上午10点", "value": "SpecWeeklyFriday10AM"}]}], "api": {"url": "/moego.bff/devops/ai-studio/updateAiStudioTask", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "data": {"&": "$$"}}, "actions": [{"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "u:c4284fcac038"}]}}, "level": "primary", "id": "u:90950460b7d9"}], "resetAfterSubmit": false}], "id": "u:0fee391c9d09", "asideResizor": false, "pullRefresh": {"disabled": true}}