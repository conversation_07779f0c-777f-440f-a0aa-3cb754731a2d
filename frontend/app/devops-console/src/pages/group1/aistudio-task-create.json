{"type": "page", "title": "ai-studio-创建任务", "body": [{"id": "u:c4284fcac038", "type": "form", "title": "表单", "mode": "flex", "labelAlign": "top", "dsType": "api", "feat": "Insert", "body": [{"name": "task", "label": "task名", "row": 0, "type": "input-text", "id": "u:9ee126a4d9ee"}, {"name": "a<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>", "row": 1, "type": "input-text", "id": "u:9ee126a4d9ee"}, {"name": "templateId", "label": "模版", "row": 2, "type": "select", "id": "u:3e134fd27e19", "multiple": false, "source": {"method": "post", "url": "/moego.bff/devops/ai-studio/listAiStudioTemplateID", "requestAdaptor": "", "adaptor": "", "messages": {}, "silent": false}, "selectFirst": true, "onEvent": {"change": {"actions": [{"actionType": "ajax", "outputVar": "responseResult", "options": {}, "api": {"url": "/moego.bff/devops/ai-studio/getAiStudioTemplate", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "data": {"id": "${event.data.value}"}}}, {"actionType": "setValue", "componentId": "u:152d15b9511c", "args": {"value": "${event.data.responseResult.envKeys|split:','|join:'=\n'}="}}]}}}, {"name": "envs", "label": "环境变量", "row": 3, "type": "textarea", "id": "u:152d15b9511c", "minRows": 3, "maxRows": 20, "labelRemark": {"icon": "fa fa-question-circle", "trigger": ["hover"], "className": "Remark--warning", "placement": "top", "title": "提示", "content": "key=value"}}, {"row": 4, "type": "input-text", "id": "u:66a6736ea22e", "colSize": "1", "label": "<strong>频道 ID：</strong>可选，如C08MXLJELBZ，结果将被推送到这个Slack中，请先在channel添加PlatformsApp应用", "name": "imChannel"}, {"row": 5, "type": "select", "colSize": "1", "label": "<strong>定时运行：</strong>可选", "name": "spec", "id": "u:99e8b63deea9", "options": [{"label": "不定时运行", "value": ""}, {"label": "每天早上10点", "value": "SpecDaily10AM"}, {"label": "每天下午6点", "value": "SpecDaily6PM"}, {"label": "每30分钟", "value": "SpecEvery30Min"}, {"label": "每5分钟", "value": "SpecEvery5Min"}, {"label": "每天15点", "value": "SpecDaily3PM"}, {"label": "周五上午10点", "value": "SpecWeeklyFriday10AM"}]}], "api": {"url": "/moego.bff/devops/ai-studio/createAiStudioTask", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "data": {"&": "$$"}}, "actions": [{"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "u:c4284fcac038"}]}}, "level": "primary", "id": "u:88ab471e7005"}], "resetAfterSubmit": true}], "id": "u:0fee391c9d09", "asideResizor": false, "pullRefresh": {"disabled": true}}