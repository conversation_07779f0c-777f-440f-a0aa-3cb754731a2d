{"type": "page", "body": [{"type": "crud", "api": {"method": "post", "url": "/moego.bff/devops/test-account/listTestAccounts", "data": {"pageSize": "${perPage}", "pageToken": "${STRIPTAG(page)}", "filter": "${filter}"}}, "syncLocation": false, "filter": {"mode": "normal", "title": "Query conditions", "data": {"filter": {}}, "body": [{"type": "input-text", "label": "Owner", "name": "filter.owner", "clearable": true, "clearValueOnEmpty": true}, {"label": "Is occupied", "name": "filter.occupied", "type": "radios", "options": [{"label": "true", "value": true}, {"label": "false", "value": false}]}, {"type": "fieldSet", "title": "Select attributes", "collapsable": true, "collapsed": true, "body": [{"type": "select", "label": "Region code", "name": "filter.attributes.regionCode", "options": [{"label": "US", "value": "US"}, {"label": "CA", "value": "CA"}, {"label": "GB", "value": "GB"}, {"label": "AU", "value": "AU"}, {"label": "CN", "value": "CN"}]}, {"name": "filter.attributes.hasSmsCredit", "label": "Has sms credit", "type": "radios", "options": [{"label": "true", "value": true}, {"label": "false", "value": false}]}, {"name": "filter.attributes.hasEmailCredit", "label": "Has email credit", "type": "radios", "options": [{"label": "true", "value": true}, {"label": "false", "value": false}]}, {"name": "filter.attributes.enableBoardingDaycare", "label": "Enable boarding daycare", "type": "radios", "options": [{"label": "true", "value": true}, {"label": "false", "value": false}]}, {"name": "filter.attributes.enableOnlineBooking", "label": "Enable online booking", "type": "radios", "options": [{"label": "true", "value": true}, {"label": "false", "value": false}]}]}], "actions": [{"type": "reset", "label": "重置"}, {"type": "submit", "label": "搜索", "level": "primary"}]}, "headerToolbar": [{"label": "新增", "type": "button", "actionType": "dialog", "level": "primary", "dialog": {"title": "新增测试账号", "body": {"type": "form", "api": {"method": "post", "url": "/moego.bff/devops/test-account/createTestAccount"}, "mode": "horizontal", "body": [{"type": "input-text", "name": "testAccount.email", "label": "Email", "placeholder": "不填则自动生成"}, {"type": "input-text", "name": "testAccount.password", "label": "Password", "placeholder": "不填则自动生成"}, {"type": "input-text", "name": "testAccount.owner", "label": "Owner", "placeholder": "如果需要给某个用例独占，可以填写用例名称"}, {"type": "checkbox", "name": "testAccount.disposable", "option": "Disposable (一次性账号，不放入测试账号池)"}, {"type": "input-sub-form", "name": "testAccount.attributes", "label": "Attributes", "btnLabel": "选择属性", "form": {"title": "选择属性", "body": [{"label": "Country", "type": "select", "name": "regionCode", "multiple": false, "value": "US", "options": [{"label": "US", "value": "US"}, {"label": "CA", "value": "CA"}, {"label": "GB", "value": "GB"}, {"label": "AU", "value": "AU"}, {"label": "CN", "value": "CN"}]}, {"name": "hasSmsCredit", "option": "Has sms credit", "type": "checkbox"}, {"name": "hasEmailCredit", "option": "Has email credit", "type": "checkbox"}, {"name": "enableBoardingDaycare", "option": "Enable boarding daycare", "type": "checkbox"}, {"name": "enableOnlineBooking", "option": "Enable online booking", "type": "checkbox"}]}}]}, "actions": [{"label": "生成", "type": "button", "level": "primary", "actionType": "confirm", "feedback": {"title": "生成结果", "body": [{"type": "property", "column": 1, "items": [{"label": "ID", "content": "${id}"}, {"label": "Email", "content": "${email}"}, {"label": "Password", "content": "${password}"}, {"label": "Owner", "content": "${owner}"}, {"label": "Disposable", "content": "${disposable}"}, {"label": "Attributes", "content": {"type": "json", "name": "attributes", "levelExpand": 0}}]}, {"type": "alert", "className": "mt-3", "body": "一次性账号不入账号池，请自行保管邮箱密码！", "level": "warning", "showIcon": true, "visibleOn": "${disposable}"}], "actions": [{"type": "button", "actionType": "close", "label": "关闭"}]}}]}}], "footerToolbar": ["statistics", "switch-per-page", "pagination"], "columns": [{"name": "id", "label": "ID"}, {"name": "email", "label": "Email"}, {"name": "password", "label": "Password"}, {"name": "owner", "label": "Owner"}, {"name": "occupied", "label": "Occupied"}, {"type": "operation", "label": "Operation", "buttons": [{"label": "属性详情", "type": "button", "level": "link", "actionType": "dialog", "dialog": {"title": "账号属性", "body": {"type": "form", "body": [{"name": "attributes", "label": "Attributes", "type": "json"}]}, "actions": []}}]}]}]}