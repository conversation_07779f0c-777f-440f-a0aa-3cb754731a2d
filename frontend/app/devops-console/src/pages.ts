import index from './pages/group1/index.json';
import testAccount from './pages/group1/test-account.json';
import aistudioTemplateTest from './pages/group1/aistudio-template-test.json'
import aistudioTemplateEdit from './pages/group1/aistudio-template-edit.json'
import aistudioTemplateList from './pages/group1/aistudio-template-list.json'
import aistudioTemplateTasks from './pages/group1/aistudio-task-list.json'
import aistudioTaskCreate from './pages/group1/aistudio-task-create.json'
import aistudioTaskEdit from './pages/group1/aistudio-task-edit.json'
import aistudioTaskLogList from './pages/group1/aistudio-tasklog-list.json'


type PageGroup = {
    label: string;
    children: Page[];
}

type Page = {
    label: string;
    url?: string;       // 当有子菜单时，可以不要 url
    schema?: any;       // 同上，有子菜单则不用提供 schema
    children?: Page[];  // 新增，用于嵌套二级菜单
    hidden?: boolean;
};

export const pages: PageGroup[] = [
    {
        label: "导航",
        children: [
            {
                label: "",
                url: "/",
                schema: index,
                hidden: true
            },
            {
                label: "测试账号池",
                url: "testAccount",
                schema: testAccount
            },
            {
                label: "AIStudio",
                children: [
                    {
                        label: "模版测试",
                        url: "aistudio-template-test",
                        schema: aistudioTemplateTest
                    },
                    {
                        label: "模版列表",
                        url: "aistudio-template-list",
                        schema: aistudioTemplateList
                    },
                    {
                        label: "模版详情",
                        url: "aistudio-template-details",
                        schema: aistudioTemplateEdit,
                        hidden: true
                    },
                    {
                        label: "任务列表",
                        url: "aistudio-tasks",
                        schema: aistudioTemplateTasks
                    },
                    {
                        label: "创建任务",
                        url: "aistudio-task-create",
                        schema: aistudioTaskCreate,
                        hidden: true
                    },
                    {
                        label: "编辑任务",
                        url: "aistudio-task-edit",
                        schema: aistudioTaskEdit,
                        hidden: true
                    },
                    {
                        label: "任务历史",
                        url: "aistudio-tasklog-list",
                        schema: aistudioTaskLogList
                    },
                ]
            },
            // {
            //     label: "页面B",
            //     url: "pageB",
            //     schema: pageB
            // }
        ]
    }
]