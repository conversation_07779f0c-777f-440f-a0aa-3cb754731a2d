// import { createBrowserHistory } from 'history';

export type EnvType = 'production' | 's1' | 't2';

export function getEnv(): EnvType {
  if (location.hostname.endsWith('.moego.pet')) {
    return 'production';
  }
  if (location.hostname.endsWith('.s1.moego.dev')) {
    return 's1';
  }
  return 't2';
}

export interface EnvConfig {
  googleClientId: string;
}

const envConfigMap: Record<EnvType, EnvConfig> = {
  production: {
    googleClientId: '165878578571-qa9rouh2m16rsh5onom9gppliluajbs0.apps.googleusercontent.com',
  },
  s1: {
    // TODO(REX): 暂时先与 t2 一致，有问题了再说
    googleClientId: '165878578571-e39pe709t2lmfl72r6d7sout678hhdck.apps.googleusercontent.com',
  },
  t2: {
    googleClientId: '165878578571-e39pe709t2lmfl72r6d7sout678hhdck.apps.googleusercontent.com',
  },
};

export function getEnvConfig(): EnvConfig {
  return envConfigMap[getEnv()];
}

// export const HISTORY = createBrowserHistory();

export function createLocation() {
  return new Proxy(location, {
    get(target, p: keyof Location) {
      return target[p];
    },
  });
}
