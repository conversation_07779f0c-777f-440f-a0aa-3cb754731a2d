import React from 'react';

import '@fortawesome/fontawesome-free/css/all.css';
import '@fortawesome/fontawesome-free/css/v4-shims.css';

import 'amis/lib/themes/cxd.css';
import 'amis/lib/helper.css';
import 'amis/sdk/iconfont.css';

import axios, { AxiosResponse } from 'axios';
import copy from 'copy-to-clipboard';
import { appendQuery, parse } from 'monofile-utilities/lib/query-string';

import { render as renderAmis, ToastComponent, AlertComponent } from 'amis';
import { alert, confirm, toast } from 'amis-ui';

import { pages } from './pages';
import { Login } from './components/Login';
import { Router, RouteConfig } from './components/Router';
import { invokeApi } from './utils/api';
import { changeHost } from './utils/host';
import { UserInfoContext } from './components/UserInfoContext';

// amis 环境配置
const env = {
    // 下面三个接口必须实现
    fetcher: ({
        url, // 接口地址
        method, // 请求方法 get、post、put、delete
        data, // 请求数据
        responseType,
        config, // 其他配置
        headers // 请求头
    }: any) => {
        // console.log(url, method, data, config, headers);
        url = changeHost(url);

        config = config || {};
        config.withCredentials = true;
        responseType && (config.responseType = responseType);

        if (config.cancelExecutor) {
            config.cancelToken = new (axios as any).CancelToken(
                config.cancelExecutor
            );
        }

        config.headers = headers || {};

        if (method !== 'post' && method !== 'put' && method !== 'patch') {
            if (data) {
                config.params = data;
            }

            return (axios as any)[method](url, config).then((response: AxiosResponse) => {
                // console.log(response.data);
                if (config.pageField && config.perPageField) {
                    // curd 组件依赖 hasNext 或者 total 字段，但是我们的接口规范使用的是 nextPageToken，需要转换一下
                    if (!response.data.total && response.data.totalSize) {
                        response.data.total = response.data.totalSize;
                    }
                    // response.data.hasNext = response.data.nextPageToken !== null && response.data.nextPageToken !== '';
                }
                return response;
            });

        } else if (data && data instanceof FormData) {
            config.headers = config.headers || {};
            config.headers['Content-Type'] = 'multipart/form-data';
        } else if (
            data &&
            typeof data !== 'string' &&
            !(data instanceof Blob) &&
            !(data instanceof ArrayBuffer)
        ) {
            data = JSON.stringify(data);
            config.headers = config.headers || {};
            config.headers['Content-Type'] = 'application/json';
        }

        return (axios as any)[method](url, data, config).then((response: AxiosResponse) => {
            // console.log(response.data);
            if (config.pageField && config.perPageField) {
                // curd 组件依赖 hasNext 或者 total 字段，但是我们的接口规范使用的是 nextPageToken，需要转换一下
                if (!response.data.total && response.data.totalSize) {
                    response.data.total = response.data.totalSize;
                }
                // response.data.hasNext = response.data.nextPageToken !== null && response.data.nextPageToken !== '';
            }
            return response;
        });
    },
    isCancel: (value: any) => (axios as any).isCancel(value),
    copy: (content: string) => {
        copy(content);
        toast.success('内容已复制到粘贴板');
    }

    // 后面这些接口可以不用实现

    // 默认是地址跳转
    // jumpTo: (
    //   location: string /*目标地址*/,
    //   action: any /* action对象*/
    // ) => {
    //   // 用来实现页面跳转, actionType:link、url 都会进来。
    // },

    // updateLocation: (
    //   location: string /*目标地址*/,
    //   replace: boolean /*是replace，还是push？*/
    // ) => {
    //   // 地址替换，跟 jumpTo 类似
    // },

    // isCurrentUrl: (
    //   url: string /*url地址*/,
    // ) => {
    //   // 用来判断是否目标地址当前地址
    // },

    // notify: (
    //   type: 'error' | 'success' /**/,
    //   msg: string /*提示内容*/
    // ) => {
    //   toast[type]
    //     ? toast[type](msg, type === 'error' ? '系统错误' : '系统消息')
    //     : console.warn('[Notify]', type, msg);
    // },
    // alert,
    // confirm,
};

const AMISComponent = () => {
    return renderAmis(
        {
            type: 'app',
            brandName: 'Devops Console',
            pages,
        },
        {
            // props...
        },
        env
    );
};

const routes: RouteConfig[] = [
    { path: '/login', component: Login },
    { path: '/', component: AMISComponent },
];

const APP = () => {
    const [info, setInfo] = React.useState({ account: null });

    const checkLogin = async () => {
        const getCookie = (name: string): string | undefined => {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                let cookie = cookies[i].trim();
                if (cookie.startsWith(name + '=')) {
                    return cookie.substring(name.length + 1);
                }
            }
            return undefined;
        };

        const jwt = getCookie('auth_token');
        let account: any = null;

        if (jwt) {
            try {
                const payload = JSON.parse(atob(jwt.split('.')[1]));
                // check expiration, if exp not exist, we think it's valid
                if (!payload.exp || payload.exp * 1000 > Date.now()) {
                    account = payload;
                }
            } catch (e) {
                // ignore invalid jwt
                console.warn('Invalid jwt in cookie, ', e);
            }
        }

        const query = parse<{ redirect?: string }>(location.search.substring(1));
        // TODO(REX): 这里需要改成实际的接口调用
        // const res = await invokeApi('/moego.admin.authentication.v1.AuthenticationService / GetAccountInfo', {});
        // const res = { account: null };
        // if (!res.account && location.pathname !== '/login') {
        if (!account && location.pathname !== '/login') {
            location.href = appendQuery('/login', {
                redirect: query.redirect || location.href.replace(location.origin, ''),
            });
        }
        return { account };
    };

    React.useEffect(() => {
        checkLogin().then(setInfo);
    }, []);

    return (
        <UserInfoContext.Provider value={info}>
            <ToastComponent key="toast" position={'top-right'} />
            <AlertComponent key="alert" />
            <Router routes={routes} />
        </UserInfoContext.Provider>
    );
}

export default APP;
