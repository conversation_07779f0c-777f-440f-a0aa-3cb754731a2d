import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    hmr: false,
    proxy: {
      '^/backend\\.proto\\.tools\\.': {
        target: `https://${process.env.GREY_VERSION ? process.env.GREY_VERSION + '-grey-' : ''}platform-tools.t2.moego.dev`,
        changeOrigin: true,
        cookieDomainRewrite: 'localhost',
        cookiePathRewrite: '/',
        autoRewrite: true,
        protocolRewrite: 'http',
      },
      '^/moego\\.admin\\.': {
        target: `https://${process.env.GREY_VERSION ? process.env.GREY_VERSION + '-grey-' : ''}mis.t2.moego.dev`,
        changeOrigin: true,
        cookieDomainRewrite: 'localhost',
        cookiePathRewrite: '/',
        autoRewrite: true,
        protocolRewrite: 'http',
      },
      '^/moego\\.bff': {
        target: `https://${process.env.GREY_VERSION ? process.env.GREY_VERSION + '-grey-' : ''}go.t2.moego.dev`,
        changeOrigin: true,
        cookieDomainRewrite: 'localhost',
        cookiePathRewrite: '/',
        autoRewrite: true,
        protocolRewrite: 'http',
      }
    },
  },
});
